#!/usr/bin/env php
<?php

use BrickLayer\Lay\Core\LayConfig;
use Elevator\AiAgent\Controller\ElevatorAgentServiceWorkerController;
use Elevator\AiAgent\Controller\ElevatorAgentWorkflowController;
use Elevator\AiAgent\Utils\FrequencyHandler;

const SAFE_TO_INIT_LAY = true;

include_once __DIR__ . "/../../../../foundation.php";

LayConfig::connect();

$service = ElevatorAgentServiceWorkerController::new();

if($service->is_processing())
    return;

$service::foundation()->agent_mock_domain();

$model = $service->init_worker();

$wkf = ElevatorAgentWorkflowController::new();

foreach ($wkf->get_active_workflows() as $workflow) {
    foreach ($workflow->frequency as $frequency) {
        if (FrequencyHandler::is_now($frequency)) {
            $service->processing($model);
            $service::foundation()->user_session()::update_session('profile_id', $workflow->agentId);
            $wkf->execute($workflow);
        }
    }
}

$service->done_processing($model);
