# Elevator

Don't like taking the stairs? Use an elevator to get there faster! This is the philosophy behind this project.
It is a no nonsense building block to get all Osai Tech projects with a PHP backend powered by `Lay` up to speed.

PHP Version: `8.1 >`

## Install

Update your `composer.json` to include the github repo

```json
 "repositories": [
    {
      "type": "vcs",
      "url": "**************:osaitech/elevator.git"
    }
  ],
```


The run this command on your terminal
```shell
composer require osaitech/elevator:dev-main
```

Setup your `.env` file

### Get Latest Migrations
```shell
php vendor/osaitech/elevator/init.php --ignore ""
```

Inside the `""` of the `ignore` tag, pass the names of bricks you don't want in your project and separate them with commas.

### The End

## Customizing
It is important to note that, this library has a lot of features in it, and as such, you should only call the bricks you need.

After creating your brick, modify the Hook to extend the `DefaultHooks` of the Elevator Brick in question and modify accordingly.
