<?php

use Brick<PERSON>ayer\Lay\Libs\LayFn;

const SAFE_TO_INIT_LAY = true;
const USING_PHINX = true;

// We don't need this internally, but it's included here in case a project doesn't have phinx installed,
// so it can initialize it without any hassle
@include_once __DIR__ . DIRECTORY_SEPARATOR . "foundation.php";

return
    [
        'paths' => [
            'migrations' => ['db/migrations', 'db/migrations/elevator'],
            'seeds' => ['db/seeds', 'db/seeds/elevator']
        ],
        'environments' => [
            'default_migration_table' => 'migration_logs',
            'default_environment' => 'development',
            'development' => [
                'adapter' => LayFn::env('DB_DRIVER'),
                'host' => LayFn::env('DB_HOST'),
                'name' => LayFn::env('DB_NAME'),
                'user' => LayFn::env('DB_USERNAME'),
                'pass' => LayFn::env('DB_PASSWORD'),
                'port' => LayFn::env('DB_PORT'),
                'charset' => LayFn::env('DB_CHARSET', 'utf8'),
            ],
        ],
        'version_order' => 'creation'
    ];
