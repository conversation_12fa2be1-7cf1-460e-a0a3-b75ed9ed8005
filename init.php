#!/usr/bin/env php
<?php

use <PERSON><PERSON><PERSON>er\Lay\BobDBuilder\Helper\Console\Console;
use BrickLayer\Lay\BobDBuilder\Helper\Console\Format\Foreground;
use BrickLayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Libs\Dir\LayDir;
use BrickLayer\Lay\Libs\LayArray;
use BrickLayer\Lay\Libs\LayFn;
use BrickLayer\Lay\Libs\Primitives\Enums\LayLoop;

const SAFE_TO_INIT_LAY = true;

include_once __DIR__ . "/../../../foundation.php";

$project = LayConfig::server_data();

if(!file_exists($project->root . "phinx.php"))
    copy(__DIR__ . "/phinx.php", $project->root . "phinx.php");

$config = $project->root . "elevator-config.json";

if(!file_exists($config)) {
    copy(__DIR__ . DIRECTORY_SEPARATOR . "elevator-config.json", $config);

    Console::log(
        "Noticed an Elevator config file was missing, so one was created for you the root of your project. 
        \nPlease review it, add the modules you want and rerun this command",
        Foreground::purple
    );

    Console::log($config);

    return;
}

define("ELV_CONFIG", json_decode(file_get_contents($config), true));

$db_dir = $project->db;
$brick_root = $project->bricks . "Elevator" . DIRECTORY_SEPARATOR;

$config = ELV_CONFIG['config'];
$modules = ELV_CONFIG['modules'];

$list_modules = LayFn::extract_cli_tag("--modules", false) ?? LayFn::extract_cli_tag("--help", false) ?? false;
$overwrite_hooks = LayFn::extract_cli_tag("--force", false) ?? $config['overwrite_hooks'] ?? false;
$overwrite_migrations = LayFn::extract_cli_tag("--force-migrations", false) ?? $config['overwrite_migrations'] ?? false;
$overwrite_foundation = LayFn::extract_cli_tag("--force-foundation", false) ?? $config['overwrite_foundation'] ?? false;

$ignore_hooks = LayFn::extract_cli_tag("--ignore-hooks", false) ?? $config['ignore_hooks'] ?? false;
$ignore_hooks = !$ignore_hooks ? empty($modules) : $ignore_hooks;

$default_foundation = $config['default_foundation'] ?? 'FoundationUtil';

if($list_modules) {
    Console::log("Elevator Module List", Foreground::green);
    LayDir::read(
        __DIR__ . "/bricks",
        function ($file, $dir, $handler, $entry) {
            if($file == "Utils") return LayLoop::CONTINUE;
            Console::log("  $file", Foreground::light_cyan);

            $migrations = $dir . DIRECTORY_SEPARATOR . $file . DIRECTORY_SEPARATOR . "migration-files.json";

            if(file_exists($migrations)) {
                Console::log("  > migrations:" .
                    implode(
                        LayArray::map(
                            json_decode(file_get_contents($migrations), true),
                            function($v) {
                                $x = explode("_", $v, 2);

                                return "\n    + " . $x[1];
                            }
                        )
                    )
                );
            }

            return LayLoop::FLOW;
        }
    );

    Console::bell();
    return;
}

function in_modules(string $file) : ?array {
    foreach (ELV_CONFIG['modules'] as $module) {
        if($file == $module['module']) return $module;
    }

    return null;
}

function class_from_migration($migration) : string
{
    $x = explode("_", $migration);

    array_shift($x);

    return str_replace(".php", "", implode(LayArray::map( $x, fn($a) => trim(ucfirst($a)) )) );
}

function hook_temp(string $brick) : string {
    return <<<TMP
<?php
declare(strict_types=1);

namespace Bricks\\Elevator\\{$brick}\\Api;

class Hook extends \\Elevator\\{$brick}\\DefaultHooks
{
    // You can overwrite whatever applies to the context of this project
    // You can extend the existing hooks by calling parent::hooks().
    // Ensure you go-to-definition for `DefaultHooks` to see what you can and can\'t do
}

TMP;
}

function update_migration_alias(string $destination, string $migration, string $alias): void
{
    $class        = class_from_migration($migration);
    $class_alias  = class_from_migration($alias);

    $updated_lines = [];

    $handle = fopen($destination, 'r');

    if (!$handle)
        throw new \RuntimeException("Unable to open file: $destination");

    while (!feof($handle)) {
        $line = fgets($handle);

        if ($line === false) break;

        $updated_line = str_replace(" $class ", " $class_alias ", $line);

        $updated_lines[] = $updated_line;
    }

    fclose($handle);

    file_put_contents($destination, implode('', $updated_lines));
}

Console::log("[+] Installing your selected modules", Foreground::light_cyan);
Console::log(" - Overwrite Hooks: " . ($overwrite_hooks ? 'true' : 'false'));
Console::log(" - Overwrite Migrations: " . ($overwrite_migrations ? 'true' : 'false'));
Console::log(" - Ignore Hooks: " . ($ignore_hooks ? 'true' : 'false'));
Console::log(" - Default Foundation: $default_foundation");
Console::log(" - Overwrite Foundation: $overwrite_foundation");

print "\n";

LayDir::read(
    __DIR__ . "/bricks",
    function ($file, $dir, $handler, $entry) use ($brick_root, $ignore_hooks, $overwrite_hooks, $overwrite_migrations, $db_dir) {
        $module = in_modules($file);
        $module_root = $entry['full_path'] . DIRECTORY_SEPARATOR;

        if(str_starts_with($file, ".") || $file == "Utils" || !$module)
            return LayLoop::CONTINUE;

        $overwrite_migrations = $module['config']['overwrite_migrations'] ?? $overwrite_migrations;
        $overwrite_hooks = $module['config']['overwrite_hooks'] ?? $overwrite_hooks;

        Console::log("[✔︎] Found: $file", Foreground::light_green);

        if(!$ignore_hooks && file_exists($module_root . "DefaultHooks.php")) {
            $dest_dir = $brick_root . $file . DIRECTORY_SEPARATOR . "Api" . DIRECTORY_SEPARATOR;
            $dest = $dest_dir . "Hook.php";
            $exists = false;

            if (!$overwrite_hooks && file_exists($dest)) {
                Console::log("    Exists already: $dest", Foreground::yellow);
                Console::log("    Use [ --force ] tag to overwrite or update your elevator config file", Foreground::purple);
                $exists = true;
            }

            if(!$exists) {
                LayDir::make($dest_dir, 0777, true);

                if (!file_put_contents($dest, hook_temp($file))) {
                    Console::log("    Could not create Hook for $file! Stopping process!", Foreground::light_red);
                    Console::bell();
                    die;
                }

                Console::log("    - Hook Generated at: $dest");
            }
        }

        Console::log("    [...] Getting migrations", Foreground::light_cyan);

        if(!file_exists($module_root . "migration-files.json")) {
            Console::log("    [x] No migrations for module");
            return LayLoop::CONTINUE;
        }

        $migration_dir = $db_dir . "migrations" . DIRECTORY_SEPARATOR . "elevator" . DIRECTORY_SEPARATOR;
        LayDir::make($migration_dir, 0777, true);

        $migration_alias = $module['migration_alias'];

        foreach (json_decode(file_get_contents($module_root . "migration-files.json"), true) as $migration) {
            if(!empty($migration_alias)) {
                $found = false;

                foreach ($migration_alias as $key => $alias) {
                    $og = $alias[0] . ".php";

                    if(!str_ends_with($migration, $og))
                        continue;

                    $found = true;
                    $og = $migration;

                    $migration = explode("_", $og, 2)[0] . "_" . $alias[1] . ".php";

                    $destination = $migration_dir . $migration;

                    unset($migration_alias[$key]);

                    if(!$overwrite_migrations && file_exists($destination)) {
                        Console::log("    Migration exists already: $destination", Foreground::yellow);
                        Console::log("    Use [ --force-migrations ] tag to overwrite migrations or update your config file", Foreground::purple);
                        continue;
                    }

                    copy(__DIR__ . "/db/migrations/$og", $destination);

                    Console::log("      - Updating migration From [$og] to [$migration]\n  $destination");
                    update_migration_alias($destination, $og, $migration);
                }

                if($found) continue;
            }

            $destination = $migration_dir . $migration;

            if(!$overwrite_migrations && file_exists($destination)) {
                Console::log("    Migration exists already: $destination", Foreground::yellow);
                Console::log("    Use [ --force-migrations ] tag to overwrite migrations or update your config file", Foreground::purple);
                continue;
            }

            copy(__DIR__ . "/db/migrations/$migration", $destination);
        }

        Console::log("    [o] Migrations copy complete!");

        Console::log("    [...] Getting Seeders", Foreground::light_cyan);

        if(!file_exists($module_root . "seeder-files.json")) {
            Console::log("    [x] No Seeder for module");
            return LayLoop::CONTINUE;
        }

        $seeder_dir = $db_dir . "seeds" . DIRECTORY_SEPARATOR . "elevator" . DIRECTORY_SEPARATOR;
        LayDir::make($seeder_dir . "data", 0777, true);

        $seeder = json_decode(file_get_contents($module_root . "seeder-files.json"), true);

        foreach ($seeder['seeds'] as $seed) {
            $destination = $seeder_dir . $seed;

            if(!$overwrite_migrations && file_exists($destination)) {
                Console::log("    Seeder exists already: $destination", Foreground::yellow);
                Console::log("    Use [ --force-migrations ] tag to overwrite seeders or update your config file", Foreground::purple);
                continue;
            }

            copy(__DIR__ . "/db/seeds/$seed", $destination);
        }

        Console::log("    [o] Seeders copy complete!");

        Console::log("    [...] Getting Seeder Data", Foreground::light_cyan);

        $seeder_dir = $db_dir . "seeds" . DIRECTORY_SEPARATOR . "elevator" . DIRECTORY_SEPARATOR . "data" . DIRECTORY_SEPARATOR;

        foreach ($seeder['data'] as $seed) {
            $destination = $seeder_dir . $seed;

            if(!$overwrite_migrations && file_exists($destination)) {
                Console::log("    Seeder Data exists already: $destination", Foreground::yellow);
                Console::log("    Use [ --force-migrations ] tag to overwrite seeders data or update your config file", Foreground::purple);
                continue;
            }

            copy(__DIR__ . "/db/seeds/data/$seed", $destination);
        }

        Console::log("    [o] Seeder Data copy complete!");

        return LayLoop::FLOW;
    }
);

if(!$ignore_hooks)
    file_put_contents(
        $brick_root . "child-has-hooks.php",
        "<?php // AUTO GEN BY Elevator. If you want to use Elevator's Hooks as is, then don't delete, else delete this file to avoid clashes!"
    );

if (!file_exists($brick_root . $default_foundation . ".php") || $overwrite_foundation)
    file_put_contents($brick_root . $default_foundation . ".php", <<<HK
<?php
declare(strict_types=1);

namespace Bricks\\Elevator;

class $default_foundation extends \\Elevator\\Utils\\ElevatorFoundation
{
    // You can overwrite whatever controller you wish.
    // Ensure you go-to-definition for `ElevatorFoundation` to see what you can and can\'t do
}

HK
    );

Console::log("Operation complete!", Foreground::blue);
Console::bell();