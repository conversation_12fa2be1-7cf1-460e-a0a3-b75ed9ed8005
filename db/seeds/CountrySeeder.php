<?php

declare(strict_types=1);

use BrickLayer\Lay\Core\LayConfig;
use Elevator\Country\Model\ElevatorCountryModel;
use Phinx\Seed\AbstractSeed;

class CountrySeeder extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run(): void
    {
        LayConfig::connect();

        $data = json_decode(file_get_contents(__DIR__ . "/data/countries.json"), true);

        (new ElevatorCountryModel)->add_batch($data, function (array $columns) {
            $columns["name"] = $columns['name'];
            $columns["iso2"] = $columns['countryCode'];
            $columns["iso3"] = $columns['countryCodeAlpha3'];
            $columns["calling_code"] = $columns['phone'];
            $columns["currency_code"] = $columns['currency'];
            $columns["flag"] = $columns['flag'];
            $columns["symbol"] = $columns['symbol'];

            unset(
                $columns["countryCode"],
                $columns["countryCodeAlpha3"],
                $columns["phone"],
                $columns["currency"],
                $columns["stateProvinces"],
            );

            return $columns;
        });

    }
}
