<?php

declare(strict_types=1);

final class BrandTable extends \Elevator\Utils\ElevatorBaseMigration
{

    protected function model_table(): string
    {
        return \Elevator\Brand\Model\ElevatorBrandModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "brand_id" => [
                "type" => "string",
                "null" => false,
                "unique" => true
            ],
            "slogan" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "name" => [
                "type" => "string",
                "length" => 150,
                "null" => true,
            ],
            "slug" => [
                "type" => "string",
                "length" => 150,
                "null" => true,
            ],
            "logo" => [
                "type" => "text",
                "null" => true,
            ],
            "cover_photo" => [
                "type" => "text",
                "null" => true,
            ],
            "email" => [
                "type" => "string",
                "null" => true,
            ],
            "support_email" => [
                "type" => "string",
                "null" => true,
            ],
            "phone" => [
                "type" => "string",
                "null" => true,
            ],
            "support_phone" => [
                "type" => "string",
                "null" => true,
            ],
            "address" => [
                "type" => "text",
                "null" => true,
            ],
            "website" => [
                "type" => "text",
                "null" => true,
            ],
            "pry_color" => [
                "type" => "string",
                "null" => true,
            ],
            "sec_color" => [
                "type" => "string",
                "null" => true,
            ],
            "personality" => [
                "type" => "text",
                "null" => true,
            ],
            "voice" => [
                "type" => "text",
                "null" => true,
            ],
            "brand_type" => [
                "type" => "string",
                "null" => true,
            ],
            "audience" => [
                "type" => "text",
                "null" => true,
            ],
            "social_links" => [
                "type" => "jsonb",
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
