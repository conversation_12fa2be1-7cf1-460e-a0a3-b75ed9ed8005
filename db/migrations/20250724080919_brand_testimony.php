<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class BrandTestimony extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Brand\Model\ElevatorBrandTestimonyModel::$table;
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['name', 'company', 'client_id'], ['unique' => true]);
    }

    protected function model_columns(): array
    {
        return [
            "brand_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false
            ],
            "name" => [
                "type" => "string",
                "length" => 150,
                "null" => false,
            ],
            "testimony" => [
                "type" => "text",
                "null" => false,
            ],
            "rating" => [
                "type" => "string",
                "null" => true,
            ],
            "company" => [
                "type" => "string",
                "length" => 150,
                "null" => true,
            ],
            "position" => [
                "type" => "string",
                "length" => 150,
                "null" => true,
            ],
            "dp" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],

        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
