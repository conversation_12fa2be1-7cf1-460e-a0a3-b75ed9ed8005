<?php

declare(strict_types=1);

use BrickLayer\Lay\Libs\FileUpload\Enums\FileUploadStorage;
use <PERSON><PERSON><PERSON>er\Lay\Libs\FileUpload\Enums\FileUploadType;
use Elevator\FileStore\Enums\BucketFileStatus;

final class FileStoreTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\FileStore\Model\ElevatorFileStoreModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "path" => [
                "type" => "text",
                "null" => true,
            ],
            "url" => [
                "type" => "text",
                "null" => true,
            ],
            "type_id" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "mime_type" => [
                "type" => "string",
                "null" => false,
            ],
            "size" => [
                "type" => "integer",
                "null" => false,
            ],
            "checksum_tmp" => [
                "type" => "string",
                "length" => 64,
            ],
            "checksum" => [
                "type" => "string",
                "length" => 64,
            ],
            "checksum_algo" => [
                "type" => "string",
            ],
            "extension" => [
                "type" => "string",
                "length" => 10,
                "null" => false,
            ],
            "upload_type" => [
                "type" => "string",
                "null" => false,
                "default" => FileUploadType::IMG->name
            ],
            "storage" => [
                "type" => "string",
                "null" => false,
                "default" => FileUploadStorage::DISK->name
            ],
            "metadata" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "is_public" => [
                "type" => "integer",
                "length" => 1,
                "null" => false,
                "default" => '1',
            ],
            "expires_at" => [
                "type" => "biginteger",
                "null" => true,
            ],
            "status" => [
                "type" => "string",
                "null" => false,
                "default" => BucketFileStatus::READY->name
            ],
            "provider" => [
                "type" => "string",
                "null" => true,
            ],

        ];
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['client_id', 'checksum_tmp'], ['unique' => true]);
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
