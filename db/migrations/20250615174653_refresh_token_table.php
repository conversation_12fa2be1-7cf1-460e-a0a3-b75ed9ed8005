<?php

declare(strict_types=1);

final class RefreshTokenTable extends \Elevator\Utils\ElevatorBaseMigration
{

    protected function model_table(): string
    {
        return \Elevator\User\Model\ElevatorRefreshTokenModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "user_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false,
            ],
            "token" => [
                "type" => "text",
                "null" => false,
            ],
            "device" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "expires_at" => [
                "type" => "biginteger",
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
