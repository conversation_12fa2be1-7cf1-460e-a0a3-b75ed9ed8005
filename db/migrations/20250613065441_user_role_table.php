<?php

declare(strict_types=1);

final class UserRoleTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\User\Model\ElevatorRoleModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "null" => false,
                "length" => 100,
            ],
            "note" => [
                "type" => "text",
                "null" => true,
            ],
            "permissions" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "is_super" => [
                "type" => "integer",
                "length" => 1,
                "default" => 0,
                "null" => false,
            ],
            "scope" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "total_users" => [
                "type" => "integer",
                "default" => 0,
                "null" => false,
            ],
        ];
    }

    protected function pre_create(\Phinx\Db\Table $migration) : void
    {
        $migration->addIndex([ "name", "client_id"], [ "unique" => true ]);
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
