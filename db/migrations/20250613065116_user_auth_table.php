<?php

declare(strict_types=1);

final class UserAuthTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table() : string
    {
        return \Elevator\User\Model\ElevatorAuthModel::$table;
    }

    protected function exclude_columns() : array
    {
        return ['client_id'];
    }

    protected function model_columns() : array
    {
        return [
            "is_bot" => [
                "type" => "integer",
                "length" => 1,
                'default' => 0,
                "null" => false,
            ],
            "last_workspace" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "show_all_workspaces" => [
                "type" => "integer",
                "length" => 1,
                'default' => 1,
                "null" => false,
            ],
            "login_name" => [
                "type" => "text",
                "null" => true,
            ],
            "login_pass" => [
                "type" => "text",
                "null" => true,
            ],
            "otp_token" => [
                "type" => "string",
                "length" => 50,
                "null" => true,
            ],
            "otp_token_expires" => [
                "type" => "biginteger",
                "null" => true,
            ],
            "is_active" => [
                "type" => "integer",
                "null" => false,
                "default" => 0,
                "length" => 1
            ],
            "reset_login_pass" => [
                "type" => "integer",
                "null" => false,
                "default" => 0
            ],
            "must_log_out" => [
                "type" => "integer",
                "null" => false,
                "default" => 0
            ],
            "has_2fa" => [
                "type" => "integer",
                "null" => false,
                "default" => 0
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
