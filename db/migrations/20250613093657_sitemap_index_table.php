<?php

declare(strict_types=1);

final class SitemapIndexTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\SitemapRss\Model\ElevatorSitemapModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "year" => [
                "type" => "integer",
                "null" => false,
            ],
            "month" => [
                "type" => "integer",
                "null" => false,
            ],
            "week" => [
                "type" => "integer",
                "null" => false,
            ],
            "timezone" => [
                "type" => "string",
                "null" => false,
            ],
            "utc_offset" => [
                "type" => "string",
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
