<?php

declare(strict_types=1);

final class CountryTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Country\Model\ElevatorCountryModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
                "unique" => true
            ],
            "iso2" => [
                "type" => "string",
                "length" => 5,
                "null" => false
            ],
            "iso3" => [
                "type" => "string",
                "null" => false
            ],
            "currency_code" => [
                "type" => "string",
            ],
            "calling_code" => [
                "type" => "string",
            ],
            "flag" => [
                "type" => "string",
                "length" => 100,
            ],
            "symbol" => [
                "type" => "string",
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
