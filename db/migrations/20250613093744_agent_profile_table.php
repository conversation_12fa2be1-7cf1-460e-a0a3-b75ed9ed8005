<?php

declare(strict_types=1);


final class AgentProfileTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\AiAgent\Model\ElevatorAgentModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "auth_id" => [
                "type" => "char",
                "null" => false,
                "length" => 36,
            ],
            "name" => [
                "type" => "string",
                "length" => 200,
                "null" => true,
            ],
            "x_permissions" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "role" => [
                "type" => "char",
                "null" => true,
                "length" => 36,
            ],
            "about" => [
                "type" => "text",
                "null" => true,
            ],
            "slug" => [
                "type" => "text",
                "null" => true,
            ],
            "dataset" => [
                "type" => "jsonb",
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
