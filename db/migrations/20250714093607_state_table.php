<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class StateTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Country\Model\ElevatorStateModel::$table;
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['name', 'country_iso3'], ['unique' => true]);
    }

    protected function model_columns(): array
    {
        return [
            "country_iso3" => [
                "type" => "string",
                "null" => false
            ],
            "name" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "capital" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "zone" => [
                "type" => "string",
            ],
            "slogan" => [
                "type" => "string",
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
