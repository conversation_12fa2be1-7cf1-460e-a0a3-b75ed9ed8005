<?php

declare(strict_types=1);

final class FileStoreTypeTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\FileStore\Model\ElevatorFileStoreTypeModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "slug" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "max_size" => [
                "type" => "integer",
                "null" => true,
            ],
            "max_dimension" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "allowed_types" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "can_delete" => [
                "type" => "integer",
                "null" => false,
                "default" => 1
            ],

        ];
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['client_id', 'name'], ['unique' => true]);
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
