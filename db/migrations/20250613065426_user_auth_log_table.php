<?php

declare(strict_types=1);


final class UserAuthLogTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\User\Model\ElevatorAuthLogModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "profile_id" => [
                "type" => "char",
                "null" => false,
                "length" => 36
            ],
            "cached_login" => [
                "type" => "integer",
                "null" => false,
                "default" => 0,
                "length" => 1
            ],
            "auth_type" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "env_info" => [
                "type" => "jsonb",
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
