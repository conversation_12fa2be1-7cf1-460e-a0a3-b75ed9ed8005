<?php

declare(strict_types=1);

final class BrandNewsletter extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Brand\Model\ElevatorBrandNewsletterModel::$table;
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['name', 'email', 'client_id'], ['unique' => true]);
    }

    protected function model_columns(): array
    {
        return [
            "brand_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false
            ],
            "name" => [
                "type" => "string",
                "length" => 150,
                "null" => false,
            ],
            "email" => [
                "type" => "string",
                "length" => 150,
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
