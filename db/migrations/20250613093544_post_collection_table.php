<?php

declare(strict_types=1);

final class PostCollectionTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Blog\Model\ElevatorPostCollection::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
                "unique" => true,
            ],
            "description" => [
                "type" => "string",
                "length" => 225,
                "null" => true,
            ],
            "slug" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "disabled" => [
                "type" => "integer",
                "length" => 1,
                "default" => 0,
                "null" => false
            ],

        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
