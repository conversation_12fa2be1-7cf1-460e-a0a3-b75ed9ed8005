<?php

declare(strict_types=1);


final class PostReactionTable extends \Elevator\Utils\ElevatorBaseMigration
{

    protected function model_table(): string
    {
        return \Elevator\Blog\Model\ElevatorPostReaction::$table;
    }

    protected function model_columns(): array
    {
        return [
            "post_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false,
                "unique" => true,
            ],
            "data_total" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "data_today" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "data_week" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "today_ends" => [
                "type" => "biginteger",
                "null" => true
            ],
            "week_ends" => [
                "type" => "biginteger",
                "null" => true
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
