<?php

declare(strict_types=1);


use Elevator\AiAgent\Enums\WorkflowStatus;

final class AgentServiceWorkerTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\AiAgent\Model\ElevatorAgentServiceWorkerModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "path" => [
                "type" => "text",
                "null" => false,
            ],
            "status" => [
                "type" => "string",
                "null" => false,
                "default" => WorkflowStatus::PAUSED->name,
            ],
            "other" => [
                "type" => "jsonb",
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
