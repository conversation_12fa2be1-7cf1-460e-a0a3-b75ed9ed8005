<?php

declare(strict_types=1);


use Elevator\AiAgent\Enums\WorkflowStatus;

final class AgentWorkflowTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\AiAgent\Model\ElevatorAgentWorkflowModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "name" => [
                "type" => "string",
                "null" => false,
                "length" => 100,
                "unique" => true
            ],
            "template" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "agent" => [
                "type" => "char",
                "length" => 36,
                "null" => false,
            ],
            "status" => [
                "type" => "string",
                "null" => false,
                "default" => WorkflowStatus::PAUSED->name
            ],
            "frequency" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "dependency" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "description" => [
                "type" => "text",
                "null" => true,
            ],
            "one_time" => [
                "type" => "integer",
                "null" => false,
                "default" => 0
            ],
            "other" => [
                "type" => "jsonb",
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
