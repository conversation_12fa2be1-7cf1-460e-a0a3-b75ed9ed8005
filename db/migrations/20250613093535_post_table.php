<?php

declare(strict_types=1);

use Elevator\Blog\Enums\PostStatus;

final class PostTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Blog\Model\ElevatorPostModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "title" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
                "unique" => true,
            ],
            "category" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "tag_id" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],

            "slug" => [
                "type" => "string",
                "length" => 120,
                "null" => false,
                "unique" => true,
            ],
            "slug_editable" => [
                "type" => "integer",
                "length" => 1,
                "default" => 1,
                "null" => false
            ],
            "cover_photo_slug" => [
                "type" => "text",
                "null" => true,
            ],
            "cover_photo" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "cover_photo_credit" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],

            "content_meta" => [
                "type" => "string",
                "length" => 300,
                "null" => true
            ],
            "content_body" => [
                "type" => "text",
                "null" => false
            ],
            "duration" => [
                "type" => "integer",
                "null" => true
            ],
            "table_of_content" => [
                "type" => "text",
                "null" => true
            ],
            "template" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],

            "post_status" => [
                "type" => "string",
                "default" => PostStatus::DRAFT->name,
                "null" => false
            ],
            "schedule_time" => [
                "type" => "biginteger",
                "null" => true
            ],

            "author_is_ai" => [
                "type" => "integer",
                "null" => false,
                "length" => 1,
                "default" => 0
            ],
            "notified_editors" => [
                "type" => "integer",
                "null" => false,
                "length" => 1,
                "default" => 0
            ],
            "notified_authors" => [
                "type" => "integer",
                "null" => false,
                "length" => 1,
                "default" => 0
            ],
            "coauthors" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "revision_note" => [
                "type" => "text",
                "null" => true,
            ],
            "calendar_id" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "autosave_blob" => [
                "type" => "jsonb",
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
