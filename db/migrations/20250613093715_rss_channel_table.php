<?php

declare(strict_types=1);


final class RssChannelTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\SitemapRss\Model\ElevatorRssChannel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "category_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
