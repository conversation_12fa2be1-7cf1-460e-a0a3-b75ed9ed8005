<?php

namespace Elevator\Brand;

use Elevator\Brand\Controller\ElevatorBrandController;
use Elevator\Brand\Controller\ElevatorBrandFaqController;
use Elevator\Brand\Controller\ElevatorBrandNewsletterController;
use Elevator\Brand\Controller\ElevatorBrandPartnerController;
use Elevator\Brand\Controller\ElevatorBrandProspectController;
use Elevator\Brand\Controller\ElevatorBrandTeamController;
use Elevator\Brand\Controller\ElevatorBrandTestimonyController;
use Elevator\Utils\ElevatorBaseHook;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorBrandController $brand;
    protected static ElevatorBrandFaqController $faqs;
    protected static ElevatorBrandTestimonyController $testimony;
    protected static ElevatorBrandPartnerController $partner;
    protected static ElevatorBrandTeamController $team;
    protected static ElevatorBrandProspectController $prospect;
    protected static ElevatorBrandNewsletterController $newsletter;

    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix . "/brand");
    }

    protected function pre_hook() : void
    {
        $foundation = self::foundation();

        self::$brand = $foundation->brand_ctrl();
        self::$faqs = $foundation->brand_faq();
        self::$testimony = $foundation->brand_testimony();
        self::$partner = $foundation->brand_partner();
        self::$team = $foundation->brand_team();
        self::$prospect = $foundation->brand_prospect();
        self::$newsletter = $foundation->brand_newsletter();
    }

    protected function hooks(): void
    {
        $this->primary_prefix();

        $this->post("/prospect")->bind(fn() => self::$prospect->add());
        $this->post("/subscribe-newsletter")->bind(fn() => self::$newsletter->add());

        $this->elevator_middleware()->is_auth();
        $this->elevator_middleware()->is_permitted(self::$brand);

        $this->post("update")->bind(fn() => self::$brand->update());

        $this->group("faqs", function () {
            $this->post("/")->bind(fn() => self::$faqs->add());
            $this->post("/bulk")->bind(fn() => self::$faqs->from_csv());
            $this->put("/")->bind(fn() => self::$faqs->update());
            $this->delete("/")->bind(fn() => self::$faqs->delete());
            $this->get("/{page}")->bind(fn($page) => self::$faqs->list($page));
        });

        $this->group("prospect", function (){
            $this->delete("/")->bind(fn() => self::$prospect->delete());
            $this->get("/{page}")->bind(fn($page) => self::$prospect->list($page));
        });

        $this->group("newsletter", function (){
            $this->delete("/")->bind(fn() => self::$newsletter->delete());
            $this->get("/{page}")->bind(fn($page) => self::$newsletter->list($page));
        });

        $this->group("testimony", function (){
            $this->post("/")->bind(fn() => self::$testimony->add());
            $this->post("/bulk")->bind(fn() => self::$testimony->from_csv());
            $this->post("/update")->bind(fn() => self::$testimony->update());
            $this->delete("/")->bind(fn() => self::$testimony->delete());
            $this->get("/{page}")->bind(fn($page) => self::$testimony->list($page));
        });

        $this->group("partner", function (){
            $this->post("/")->bind(fn() => self::$partner->add());
            $this->post("/update")->bind(fn() => self::$partner->update());
            $this->delete("/")->bind(fn() => self::$partner->delete());
            $this->get("/{page}")->bind(fn($page) => self::$partner->list($page));
        });

        $this->group("team", function (){
            $this->post("/")->bind(fn() => self::$team->add());
            $this->post("/bulk")->bind(fn() => self::$team->from_csv());
            $this->post("/update")->bind(fn() => self::$team->update());
            $this->put("/change-order")->bind(fn() => self::$team->change_order());
            $this->delete("/")->bind(fn() => self::$team->delete());
            $this->get("/{page}")->bind(fn($page) => self::$team->list($page));
        });

    }
}