<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Model\ElevatorBrandTeamModel;
use Elevator\Brand\Requests\ChangeTeamOrderRequest;
use Elevator\Brand\Requests\NewTeamRequest;
use Elevator\Brand\Requests\UpdateTeamRequest;
use Elevator\Brand\Resources\TeamResource;
use Elevator\Brand\Utils\Functions;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorBrandTeamController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandTeamModel
    {
        return new ElevatorBrandTeamModel();
    }

    public function from_csv() : array
    {
        $last_order = $this->model()->last_order(self::foundation()->brand_ctrl()->current_brand()->id);

        return Functions::from_csv(
            $this,
            "team members",
            function($head) use (&$last_order) {
                $name = $head['name'];
                $position = $head['position'];
                $about = $head['about'];
                $intro = $head['intro'];

                unset($head['name'], $head['position'], $head['about'], $head['intro']);

                $social = json_encode($head);

                return [
                    "order" => $last_order++,
                    "name" => $name,
                    "slug" => $this->model()->gen_id($name),
                    "position" => $position,
                    "intro" => $intro,
                    "about" => $about,
                    "social" => $social,
                ];
            }
        );
    }

    public function add() : array
    {
        $request = new NewTeamRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        if($model->is_duplicate($request))
            return self::res_warning("Team member exists already!");

        $model->new_team($request);

        if($model->is_empty())
            return self::res_warning("Could not create team member at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "New Team Member [$model->id] was created"
        );

        return self::res_success("New Team Member [$request->name] created successfully");
    }

    public function update() : array
    {
        $request = new UpdateTeamRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model()->fill($request->id);

        $edited = $model->edit_self($request);

        if(!$edited)
            return self::res_warning("Could not update team member at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Team member [$model->id] was updated"
        );

        return self::res_success("Team member [$request->name] updated successfully");
    }

    public function change_order() : array
    {
        $request = new ChangeTeamOrderRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model()->fill($request->id);

        $edited = $model->update_order($request->id, (int) $model->order, (int) $request->order);

        if(!$edited)
            return self::res_warning("Could not reorder team member at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Team member [$model->id] was reordered from $model->order to $request->order"
        );

        return self::res_success("Team member [$model->name] reordered successfully");
    }

    public function delete() : array
    {
        $id = self::request()->id;

        $model = $this->model()->fill($id);

        if (!$model->delete_self())
            return self::res_warning("Could not delete team member at the moment, please try again later");

        if ($this->model()::orm()->query_info['has_data'])
            $this->activity_log(LogActivityTypes::DELETE, "Team member Deleted: [$model->id]");

        return self::res_success("Team member [$model->name] deleted successfully");

    }

    public function by_slug(string $slug) : ?TeamResource
    {
        $data = $this->model()->get_by('slug', self::clean($slug));

        if($data->is_empty())
            return null;

        return new TeamResource($data);
    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true) : array
    {
        return TeamResource::collect(
            $this->model()->get_members(self::foundation()->brand_ctrl()->current_brand()->id, $page, $limit),
            as_array: $as_array
        );
    }
}
