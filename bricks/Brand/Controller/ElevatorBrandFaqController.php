<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Model\ElevatorBrandFaqModel;
use Elevator\Brand\Requests\NewFaqRequest;
use Elevator\Brand\Requests\UpdateFaqRequest;
use Elevator\Brand\Resources\FaqResource;
use Elevator\Brand\Utils\Functions;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorBrandFaqController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandFaqModel
    {
        return new ElevatorBrandFaqModel();
    }

    public function from_csv() : array
    {
        return Functions::from_csv(
            $this,
            "FAQs",
            fn($head) => [
                "question" => $head['Question'],
                "answer" => $head['Answer'],
            ]
        );
    }

    public function add() : array
    {
        $request = new NewFaqRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        if($model->is_duplicate($request))
            return self::res_warning("Question exists already!");

        $request->new_key("brand_id", self::foundation()->brand_ctrl()->current_brand()->id);

        $model->add($request);

        if($model->is_empty())
            return self::res_warning("Could not create FAQ at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "New FAQ [$model->id] was created"
        );

        return self::res_success("New FAQ created successfully");
    }

    public function update() : array
    {
        $request = new UpdateFaqRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model()->fill($request->id);

        if($model->is_empty())
            return self::res_warning("Question does not exist!");

        $edited = $model->edit_self($request);

        if(!$edited)
            return self::res_warning("Could not update FAQ at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "FAQ [$model->id] was updated"
        );

        return self::res_success("FAQ updated successfully");
    }

    public function delete() : array
    {
        $id = self::request()->id;

        $model = $this->model()->fill($id);

        if (!$model->delete_self())
            return self::res_warning("Could not delete FAQ at the moment, please try again later");

        if ($this->model()::orm()->query_info['has_data'])
            $this->activity_log(LogActivityTypes::DELETE, "FAQ Deleted: [$model->id]");

        return self::res_success("Question deleted successfully");

    }

    public function list(int $page = 1, int $limit = 100) : array
    {
        return FaqResource::collect(
            $this->model()
            ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
            ->all_by_col("brand_id", self::foundation()->brand_ctrl()->current_brand()->id)
        );
    }
}
