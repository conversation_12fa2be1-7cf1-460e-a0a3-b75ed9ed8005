<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Model\ElevatorBrandPartnerModel;
use Elevator\Brand\Model\ElevatorBrandTestimonyModel;
use Elevator\Brand\Requests\NewPartnerRequest;
use Elevator\Brand\Requests\NewTestimonyRequest;
use Elevator\Brand\Requests\UpdatePartnerRequest;
use Elevator\Brand\Requests\UpdateTestimonyRequest;
use Elevator\Brand\Resources\PartnerResource;
use Elevator\Brand\Utils\Functions;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorBrandPartnerController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandPartnerModel
    {
        return new ElevatorBrandPartnerModel();
    }

    public function add() : array
    {
        $request = new NewPartnerRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        if($model->is_duplicate($request))
            return self::res_warning("Partner exists already!");

        $request->new_key("brand_id", self::foundation()->brand_ctrl()->current_brand()->id);

        $model->add($request);

        if($model->is_empty())
            return self::res_warning("Could not create partner at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "New Partner [$model->id] was created"
        );

        return self::res_success("New Partner created successfully");
    }

    public function update() : array
    {
        $request = new UpdatePartnerRequest();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model()->fill($request->id);

        $edited = $model->edit_self($request);

        if(!$edited)
            return self::res_warning("Could not update partner at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Partner [$model->id] was updated"
        );

        return self::res_success("Partner updated successfully");
    }

    public function delete() : array
    {
        $id = self::request()->id;

        $model = $this->model()->fill($id);

        if (!$model->delete_self())
            return self::res_warning("Could not delete partner at the moment, please try again later");

        if ($this->model()::orm()->query_info['has_data'])
            $this->activity_log(LogActivityTypes::DELETE, "Partner Deleted: [$model->id]");

        return self::res_success("Partner deleted successfully");

    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true) : array
    {
        return PartnerResource::collect(
            $this->model()
                ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
                ->all_by_col("brand_id", self::foundation()->brand_ctrl()->current_brand()->id),
            as_array: $as_array
        );
    }
}
