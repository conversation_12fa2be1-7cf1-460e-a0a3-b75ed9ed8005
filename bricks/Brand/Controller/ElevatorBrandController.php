<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Brand\Model\ElevatorBrandModel;
use Elevator\Brand\Requests\UpdateBrandRequest;
use Elevator\Brand\Resources\BrandResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorBrandController extends ElevatorController implements HasPermission, HasLog
{
    /**
     * @abstract  You can overwrite to use your own
     */
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandModel
    {
        return new ElevatorBrandModel();
    }

    public function store(?string $current_brand = null) : string
    {
        $current_brand ??= $this->current_brand()->brandId;

        return "brand/" . $current_brand . "/";
    }

    public function current_brand() : ?BrandResource
    {
        $brand = $this->model()->one_item();

        if($brand->is_empty()) return null;

        return new BrandResource($brand);
    }

    public function update() : array
    {
        $request = new UpdateBrandRequest();

        if($request->error)
            return self::res_warning($request->error);

        $brand = $this->model();

        if($request->id)
            $brand->fill($request->id);
        else
            $brand->fill($this->current_brand()?->id);

        if($brand->is_empty())
            $done = $brand->add($request)->exists();
        else
            $done = $brand->edit_self($request);

        if(!$done)
            return self::res_warning("Could not update your brand details at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Brand details updated successfully"
        );

        return self::res_success("Brand details updated successfully");
    }

}
