<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Model\ElevatorBrandProspectModel;
use Elevator\Brand\Requests\NewProspectRequest;
use Elevator\Brand\Resources\ProspectResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorBrandProspectController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;
    use HasFoundation;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandProspectModel
    {
        return new ElevatorBrandProspectModel();
    }

    protected function body_format(NewProspectRequest $request) : array
    {
        return [
            "subject" => $request->subject,
            "tel" => $request->tel,
            "body" => $request->message,
            "date" => LayDate::now(),
        ];
    }

    protected function message_format(NewProspectRequest $request) : string
    {
        return <<<BODY
        <h3>$request->subject</h3>
        <p><b>Phone Number:</b> $request->tel</p>
        <p>$request->message</p>
        BODY;
    }

    protected function add_request_class() : NewProspectRequest
    {
        return new NewProspectRequest();
    }

    public function add() : array
    {
        $request = $this->add_request_class();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        $body = [];
        $body[] = $this->body_format($request);

        if ($model->is_duplicate($request)) {
            $data = $model->body ?? [];
            $data = [...$body, ...$data];

            if($model->edit_self([ "body" => json_encode($data) ]))
                return self::res_success("Your request has been placed successfully. This is not your first rodeo. We will surely get back to you within 2 business working days. Thank you and best regards.");

            return self::res_warning("Could not complete process at the moment, please try again later");
        }

        $model->add([
            "name" => $request->name,
            "email" => $request->email,
            "tel" => $request->tel,
            "body" => json_encode($body),
            "brand_id" => self::foundation()->brand_ctrl()->current_brand()->id
        ]);

        if ($model->is_empty())
            return self::res_warning("Could not complete process at the moment, please try again later");

        $team = self::foundation()->support_team();

        self::foundation()->mailer()->mailer
            ->subject("Website Enquiry: " . $request->subject)
            ->body($this->message_format($request))
            ->client($request->email, $request->name)
            ->server($team['mail'], $team['name'])
            ->to_server();

        return self::res_success("Your request has been placed successfully. We will surely get back to you within 2 business working days. Thank you and best regards.");
    }

    public function delete() : array
    {
        $id = self::request()->id;

        $model = $this->model()->fill($id);

        if (!$model->delete_self())
            return self::res_warning("Could not delete prospect at the moment, please try again later");

        if ($this->model()::orm()->query_info['has_data'])
            $this->activity_log(LogActivityTypes::DELETE, "Prospect Deleted: [$model->id]");

        return self::res_success("Prospect [$model->name] deleted successfully");

    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true) : array
    {
        return ProspectResource::collect(
            $this->model()
                ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
                ->all_by_col("brand_id", self::foundation()->brand_ctrl()->current_brand()->id),
            as_array: $as_array
        );
    }
}
