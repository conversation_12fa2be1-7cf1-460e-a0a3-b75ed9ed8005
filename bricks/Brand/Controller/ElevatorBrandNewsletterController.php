<?php
declare(strict_types=1);

namespace Elevator\Brand\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Model\ElevatorBrandNewsletterModel;
use Elevator\Brand\Requests\NewNewsletterRequest;
use Elevator\Brand\Resources\NewsletterResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorBrandNewsletterController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;
    use HasFoundation;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Brand;
    }


    protected function add_request_class() : NewNewsletterRequest
    {
        return new NewNewsletterRequest();
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorBrandNewsletterModel
    {
        return new ElevatorBrandNewsletterModel();
    }

    public function add() : array
    {
        $request = $this->add_request_class();

        if($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        if($model->is_duplicate($request))
            return self::res_success("Congratulations, you are already subscribed!");

        $request->new_key("brand_id", self::foundation()->brand_ctrl()->current_brand()->id);

        $model->add($request);

        if ($model->is_empty())
            return self::res_warning("Could not complete process at the moment, please try again later");

        self::foundation()->mailer()->welcome_newsletter([
            "name" => $request->name,
            "email" => $request->email,
        ]);

        return self::res_success("Congratulations! You have successfully subscribed! We will get in touch with you  with the latest tips, tricks, offers and freebies. We only dish out quality content, we don't spam");
    }

    public function delete() : array
    {
        $id = self::request()->id;

        $model = $this->model()->fill($id);

        if (!$model->delete_self())
            return self::res_warning("Could not delete newsletter member at the moment, please try again later");

        if ($this->model()::orm()->query_info['has_data'])
            $this->activity_log(LogActivityTypes::DELETE, "Newsletter Subscriber Deleted: [$model->id]");

        return self::res_success("Subscriber [$model->name] deleted successfully");

    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true) : array
    {
        return NewsletterResource::collect(
            $this->model()
                ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
                ->all_by_col("brand_id", self::foundation()->brand_ctrl()->current_brand()->id),
            as_array: $as_array
        );
    }
}
