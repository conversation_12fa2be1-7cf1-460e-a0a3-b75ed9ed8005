<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string|null id
 * @property string|null brand_id
 * @property string name
 * @property string|null logo
 * @property string|null cover_photo
 * @property string personality
 * @property string audience
 * @property string email
 * @property string phone
 * @property string|null support_email
 * @property string|null support_phone
 * @property string|null address
 * @property string|null website
 * @property string|null pry_color
 * @property string|null sec_color
 * @property string|null social_links
 */
class UpdateBrandRequest extends RequestHelper
{
    use HasFoundation;

    protected function pre_validate() : void
    {
        self::vcm_start(self::request(), [
            'required' => false,
        ]);
    }

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', "is_uuid" => true,
            "must_validate" => fn($v) => self::foundation()->brand_ctrl()->model()->fill($v)->exists()
        ]);
        $this->vcm([ 'field' => 'name', "required" => true ]);

        $file_type = self::foundation()->file_store_type()->type_by_slug("brand-logo");

        self::vcm_new_entry("brand_id", "");

        if($this->get('id'))
            self::vcm_update_entry("brand_id", self::foundation()->brand_ctrl()->model()->fill($this->get('id'))->brand_id);
        else
            self::vcm_update_entry("brand_id", self::foundation()->brand_ctrl()->model()->gen_id());

        self::vcm_rules([
            "required" => false,
            "allowed_types" => $file_type->allowed_types,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "sub_dir" => self::foundation()->brand_ctrl()->store($this->get('brand_id')),
            "bucket_url" => self::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => self::foundation()->file_store()->check_duplicate($tmp_file, $file),
        ]);

        $this->vcm([
            'field' => 'logo', "is_file" => true,
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('name') . " Brand Photo",
                    "provider" => FileModelProviderDefault::BRAND,
                    "is_public" => true
                ])
        ]);

        $file_type = self::foundation()->file_store_type()->type_by_slug("brand-cover-photo");

        $this->vcm([
            'field' => 'cover_photo', "is_file" => true,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('name') . " Brand Cover Photo",
                    "provider" => FileModelProviderDefault::BRAND,
                    "is_public" => true
                ])
        ]);

        $this->vcm([ 'field' => 'personality', "required" => true ]);
        $this->vcm([ 'field' => 'audience', "required" => true ]);

        $this->vcm([ 'field' => 'email', 'is_email' => true, "required" => true]);
        $this->vcm([ 'field' => 'phone', 'is_num' => true, "required" => true]);

        $this->vcm([ 'field' => 'support_email', 'is_email' => true,]);
        $this->vcm([ 'field' => 'support_phone', 'is_num' => true,]);

        $this->vcm([ 'field' => 'address',]);
        $this->vcm([ 'field' => 'website',]);
        $this->vcm([ 'field' => 'pry_color',]);
        $this->vcm([ 'field' => 'sec_color',]);
        $this->vcm([ 'field' => 'social_links[]' ]);
    }

}