<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string name
 * @property string testimony
 * @property string|null company
 * @property string|null position
 * @property string|null dp
 */
class NewTestimonyRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([ 'field' => 'name' ]);
        $this->vcm([ 'field' => 'testimony' ]);
        $this->vcm([ 'field' => 'company', 'required' => false ]);
        $this->vcm([ 'field' => 'position', 'required' => false ]);

        $file_type = self::foundation()->file_store_type()->type_by_slug("user-dp");

        $this->vcm([
            'field' => 'dp', "is_file" => true, "required" => false,
            "new_file_name" => $this->get('name') . " Photo",
            "allowed_types" => $file_type->allowed_types,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "sub_dir" => self::foundation()->brand_ctrl()->store() . "testimony" . DIRECTORY_SEPARATOR,
            "bucket_url" => self::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => self::foundation()->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('name') . " Testimony DP",
                    "provider" => FileModelProviderDefault::BRAND_TESTIMONY,
                    "is_public" => true
                ])
        ]);
    }

}