<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string question
 * @property string answer
 */
class NewFaqRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([ 'field' => 'question' ]);
        $this->vcm([ 'field' => 'answer' ]);
    }

}