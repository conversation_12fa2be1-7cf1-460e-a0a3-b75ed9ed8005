<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string name
 * @property string email
 * @property string|null tel
 * @property string subject
 * @property string message
 */
class NewProspectRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([ 'field' => 'name' ]);
        $this->vcm([ 'field' => 'email', 'is_email' => true ]);
        $this->vcm([ 'field' => 'tel', 'is_num' => true, 'required' => false ]);
        $this->vcm([ 'field' => 'subject' ]);
        $this->vcm([ 'field' => 'message', 'after_clean' => fn($v) => nl2br($v) ]);
    }

}