<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string id
 * @property int order
 */
class ChangeTeamOrderRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', 'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => self::foundation()->brand_team()->model()->fill($v)->exists(),
                "message" => "Invalid team member received"
            ]
        ]);

        $this->vcm([ 'field' => 'order', 'is_num' => true, 'min_value' => 1 ]);
    }

}