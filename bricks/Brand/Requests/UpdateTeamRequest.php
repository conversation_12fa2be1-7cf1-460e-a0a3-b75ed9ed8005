<?php

namespace Elevator\Brand\Requests;

use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string id
 */
class UpdateTeamRequest extends NewTeamRequest
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', 'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => self::foundation()->brand_team()->model()->fill($v)->exists(),
                "message" => "Invalid team member received"
            ]
        ]);

        $this->dp_required = false;

        parent::rules();
    }

}