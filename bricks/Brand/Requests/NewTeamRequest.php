<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string position
 * @property string|null intro
 * @property string|null about
 * @property string|null social
 * @property string dp
 */
class NewTeamRequest extends RequestHelper
{
    use HasFoundation;

    protected bool $dp_required = true;

    protected function rules(): void
    {
        $this->vcm([ 'field' => 'name' ]);
        $this->vcm([ 'field' => 'position' ]);
        $this->vcm([ 'field' => 'intro', 'required' => false ]);
        $this->vcm([ 'field' => 'about', 'required' => false ]);
        $this->vcm([ 'field' => 'social[]', 'required' => false ]);

        $file_type = self::foundation()->file_store_type()->type_by_slug("user-dp");

        $this->vcm([
            'field' => 'dp', "is_file" => true, "required" => $this->dp_required,
            "new_file_name" => $this->get('name') . " dp",
            "allowed_types" => $file_type->allowed_types,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "sub_dir" => self::foundation()->brand_ctrl()->store() . "team" . DIRECTORY_SEPARATOR,
            "bucket_url" => self::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => self::foundation()->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('name') . " DP",
                    "provider" => FileModelProviderDefault::BRAND_TEAM,
                    "is_public" => true
                ])
        ]);
    }

    protected function post_validate(array $data): array
    {
        $data["brand_id"] = self::foundation()->brand_ctrl()->current_brand()->id;

        return $data;
    }

}