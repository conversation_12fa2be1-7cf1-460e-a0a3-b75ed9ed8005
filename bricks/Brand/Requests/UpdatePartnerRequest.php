<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string id
 */
class UpdatePartnerRequest extends NewPartnerRequest
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', 'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => self::foundation()->brand_partner()->model()->fill($v)->exists(),
                "message" => "Invalid partner received"
            ]
        ]);

        $this->logo_required = false;

        parent::rules();
    }

}