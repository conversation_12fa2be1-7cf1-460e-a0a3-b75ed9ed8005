<?php

namespace Elevator\Brand\Requests;

use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string id
 */
class UpdateFaqRequest extends NewFaqRequest
{
    use HasFoundation;

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', 'is_uuid' => true,
            'must_validate' => fn($v) => self::foundation()->brand_faq()->model()->fill($v)->exists()
        ]);

        parent::rules();
    }

}