<?php

namespace Elevator\Brand\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string name
 * @property string|null website
 * @property string logo
 */
class NewPartnerRequest extends RequestHelper
{
    use HasFoundation;

    protected bool $logo_required = true;

    protected function rules(): void
    {
        $this->vcm([ 'field' => 'name' ]);
        $this->vcm([ 'field' => 'website', 'required' => false ]);

        $file_type = self::foundation()->file_store_type()->type_by_slug("brand-logo");

        $this->vcm([
            'field' => 'logo', "is_file" => true, "required" => $this->logo_required,
            "new_file_name" => $this->get('name') . " Logo",
            "allowed_types" => $file_type->allowed_types,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "sub_dir" => self::foundation()->brand_ctrl()->store() . "partners" . DIRECTORY_SEPARATOR,
            "bucket_url" => self::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => self::foundation()->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('name') . " Logo",
                    "provider" => FileModelProviderDefault::BRAND_PARTNER,
                    "is_public" => true
                ])
        ]);
    }

}