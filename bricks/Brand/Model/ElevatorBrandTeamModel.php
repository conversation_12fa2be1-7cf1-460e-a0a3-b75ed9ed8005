<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use <PERSON>Layer\Lay\Libs\ID\Gen;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Brand\Requests\NewTeamRequest;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string slug
 * @property string position
 * @property string|null dp
 * @property string|null intro
 * @property string|null about
 * @property array social
 * @property int order
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 *
 * // Joint Properties
 * @property string dp_src
 */
class ElevatorBrandTeamModel extends BaseModelHelper
{
    public static string $table = "elevator_brand_teams";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
                fn(SQL $db) => $db
                    ->and_where('client_id', $columns['client_id'] ?? $this->client_id())
            )->count("name", $columns['name']) > 0;
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name', 'client_id'],
            update_columns: ['position', 'intro', 'about', 'order', 'social', 'deleted'],
            action: 'UPDATE'
        );
    }

    protected function cast_schema() : void
    {
        $this->cast("social", "array", []);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->file_store()->model(), "dp")->use("url", "dp_src");
    }

    public function gen_id(string $name) : string
    {
        return Gen::new()->db_confirm(
            static::$table,
            "slug",
            fn (SQL $db) => $db->and_where("client_id", $this->client_id())
        )->slug($name);
    }

    public function new_team(NewTeamRequest $request) : self
    {
        $request->new_key('order', $this->last_order($request->brand_id) + 1);
        $request->new_key('slug', $this->gen_id($request->name));

        return $this->add($request, true);
    }

    public function get_members(string $brand_id, int $page = 1, int $limit = 100) : array
    {
        return $this->pre_run(
            fn(SQL $db) => $db->limit($limit, $page)->sort(static::$table . ".order")
        )
            ->all_by_col("brand_id", $brand_id);
    }

    public function last_order(string $brand_id) : int
    {
        $db = static::db();

        $db->where(static::$table . ".deleted", '0');
        $db->and_where("brand_id", $brand_id);

        $db->sort("order", "desc");
        $db->sort(static::$table . ".id");

        $db->column($this->fillable($db));

        if($res = $db->assoc()->select())
            return (int) ($this->fill($res)->order ?? 0);

        return 0;
    }

    public function update_order(string $id, int $old_order, int $new_order) : bool
    {
        if($old_order == $new_order) return true;

        return SQL::scoped_transaction(function () use ($id, $old_order, $new_order) {
            $db = static::db();
            $order = $db::escape_identifier("order");

            $db->column(['order' => $new_order])->where("id", $id)->then_update();

            $db = static::db();
            $db->where("id", "!=", $id);

            // reorder from new order down to the end
            if($new_order > $old_order) {
                $op = "-";
                $db->and_where("order", "<=", "$new_order");
                $db->and_where("order", ">", "$old_order");
            }

            // reorder from new order down to the top
            else {
                $op = "+";
                $db->and_where("order", ">=", "$new_order");
                $db->and_where("order", "<", "$old_order");
            }

            $db->column($order . " = " . $order . " $op 1");

            $updated = $db->then_update();

            return [
                'status' => $updated ? 'success' : 'warning',
                'data' => $updated
            ];
        })['data'];
    }
}
