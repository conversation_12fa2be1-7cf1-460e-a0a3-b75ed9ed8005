<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON>Layer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string email
 * @property string|null tel
 * @property array body
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 *
 */
class ElevatorBrandProspectModel extends BaseModelHelper
{
    public static string $table = "elevator_brand_prospects";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
            fn(SQL $db) => $db
                ->and_where("email", $columns['email'])
                ->and_where("deleted", '0')
                ->and_where('client_id', $columns['client_id'] ?? $this->client_id())
        )->get_by("name", $columns['name'])->exists();
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name', 'email', 'client_id'],
        );
    }

    protected function cast_schema(): void
    {
        $this->cast("body", "array");
    }
}
