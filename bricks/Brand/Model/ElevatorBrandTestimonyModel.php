<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON>L<PERSON>er\Lay\Libs\Primitives\Abstracts\RequestHelper;
use <PERSON><PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string testimony
 * @property string|null company
 * @property string|null position
 * @property string|null dp
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 *
 * // Joint Properties
 * @property string dp_src
 */
class ElevatorBrandTestimonyModel extends BaseModelHelper
{
    public static string $table = "elevator_brand_testimonies";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
                function(SQL $db) use ($columns) {
                    $db->and_where('client_id', $columns['client_id'] ?? $this->client_id());

                    if(isset($columns['company']))
                        $db->and_where('company', $columns['company']);
                }
            )->count("name", $columns['name']) > 0;
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name', 'company', 'client_id'],
            update_columns: ['testimony', 'deleted'],
            action: 'UPDATE'
        );
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->file_store()->model(), "dp")
            ->use("url", "dp_src");
    }
}
