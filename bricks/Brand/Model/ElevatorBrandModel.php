<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use BrickLayer\Lay\Libs\ID\Gen;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\Brand\Enums\BrandTypes;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string|null slogan
 * @property string|null slug
 * @property string|null logo
 * @property string|null cover_photo
 * @property string|null email
 * @property string|null phone
 * @property string|null support_email
 * @property string|null support_phone
 * @property string|null address
 * @property string|null website
 * @property string|null pry_color
 * @property string|null sec_color
 * @property string|null personality
 * @property string|null audience
 * @property string|null voice
 * @property BrandTypes|null brand_type
 * @property array social_links
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 *
 * // Joint properties
 * @property string logo_url
 * @property string cover_photo_url
 */
class ElevatorBrandModel extends BaseModelHelper
{
    public static string $table = "elevator_brands";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("social_links", "array", []);
        $this->cast("brand_type", BrandTypes::class, default_value: null, parser: fn($v) => BrandTypes::to_enum($v));
    }

    protected function prefill() : void
    {
        $file = self::foundation()->file_store()->model();

        $this->join($file, "logo")->use("url", "logo_url");
        $this->join($file, "cover_photo")->use("url", "cover_photo_url");
    }

    public function gen_id() : string
    {
        return Gen::new()->length(9)
            ->prepend('ELV-BRD-')
            ->db_confirm(self::$table, 'brand_id')
            ->gen();
    }

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
                fn(SQL $db) => $db->and_where('client_id', $columns['client_id'] ?? $this->client_id())
            )->count("name", $columns['name']) > 0;
    }

    public function add(array|RequestHelper $columns, bool $resolve_conflict = false): static
    {
        $columns = $this->req_2_array($columns);

        $columns['brand_id'] ??= $this->gen_id();
        $columns['slug'] = Gen::new()->db_confirm(static::$table, "slug")->slug($columns['name']);

        return parent::add($columns);
    }
}
