<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON>Layer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string name
 * @property string|null website
 * @property string|null logo
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 *
 * // Joint Properties
 * @property string logo_src
 */
class ElevatorBrandPartnerModel extends BaseModelHelper
{
    public static string $table = "elevator_brand_partners";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
                fn(SQL $db) => $db
                    ->and_where('client_id', $columns['client_id'] ?? $this->client_id())
            )->count("name", $columns['name']) > 0;
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name', 'client_id'],
            update_columns: ['website', 'deleted'],
            action: 'UPDATE'
        );
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->file_store()->model(), "logo")->use("url", "logo_src");
    }
}
