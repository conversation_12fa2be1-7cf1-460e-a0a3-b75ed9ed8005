<?php
declare(strict_types=1);

namespace Elevator\Brand\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\RequestHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string brand_id
 * @property string question
 * @property string answer
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 */
class ElevatorBrandFaqModel extends BaseModelHelper
{
    public static string $table = "elevator_brand_faqs";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->pre_run(
                fn(SQL $db) => $db->and_where('client_id', $columns['client_id'] ?? $this->client_id())
            )->count("question", $columns['question']) > 0;
    }

    public function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['question', 'client_id'],
            update_columns: ['answer', 'deleted'],
            action: 'UPDATE'
        );
    }
}
