<?php

namespace Elevator\Brand\Utils;

use BrickLayer\Lay\Core\Api\Enums\ApiStatus;
use BrickLayer\Lay\Libs\LayCSV;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;

class Functions
{
    public static function from_csv(ElevatorController $ctrl, string $name, callable $heads) : array
    {
        $file = $_FILES['file']['tmp_name'] ?? null;

        if(!$file)
            return $ctrl::res_warning("Failed to process bulk $name: File not received!");

        $foundation = $ctrl::foundation();
        $length = 0;

        $brand_id = $foundation->brand_ctrl()->current_brand()->id;
        $created_by = $foundation->user_session()::current_user();
        $client_id = $foundation->user_session()::current_client();

        $csv = LayCSV::process($file, function ($row, $head, $line, &$error) use (&$length, $foundation, $brand_id, $created_by, $client_id, $heads) {
            $length++;

            $cols = [
                "brand_id" => $brand_id,
                "created_by" => $created_by,
                "client_id" => $client_id,
            ];

            return array_merge($cols, $heads($head));
        }, 1024);

        if(!ApiStatus::is_ok($csv['code']))
            return $csv;

        $model = $ctrl->model();

        $added = $model->add_batch($csv['data']);

        if (!$added)
            return $ctrl::res_warning();

        if(SQL::new()->query_info['has_data'])
            $ctrl->activity_log(
                LogActivityTypes::CREATE,
                "$length $name uploaded via bulk upload",
            );

        return $ctrl::res_success("$length $name uploaded successfully");
    }
}