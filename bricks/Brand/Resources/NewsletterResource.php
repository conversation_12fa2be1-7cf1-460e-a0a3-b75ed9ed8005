<?php

namespace Elevator\Brand\Resources;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandNewsletterModel;

/**
 * @property string id
 * @property string name
 * @property string email
 * @property string date
 */
class NewsletterResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandNewsletterModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "email" => $data->email,
            "date" => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}