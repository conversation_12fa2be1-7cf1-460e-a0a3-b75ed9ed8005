<?php

namespace Elevator\Brand\Resources;

use BrickLayer\Lay\Core\View\ViewSrc;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandPartnerModel;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string id
 * @property string name
 * @property string logo
 * @property string website
 */
class PartnerResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandPartnerModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "logo" => $data->logo_src ? ViewSrc::gen($data->logo_src) : null,
            "website" => $data->website,
        ];
    }
}