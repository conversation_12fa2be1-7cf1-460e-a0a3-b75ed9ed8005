<?php

namespace Elevator\Brand\Resources;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandProspectModel;

/**
 * @property string id
 * @property string name
 * @property string email
 * @property string tel
 * @property array body
 * @property string dateCreated
 * @property string dateUpdated
 */
class ProspectResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandProspectModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "email" => $data->email,
            "tel" => $data->tel,
            "body" => $data->body,
            "dateCreated" => LayDate::date($data->created_at, format_index: 3),
            "dateUpdated" => LayDate::date($data->updated_at, format_index: 3),
        ];
    }
}