<?php

namespace Elevator\Brand\Resources;

use <PERSON><PERSON><PERSON>er\Lay\Core\View\ViewSrc;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandModel;

/**
 * @property string id
 * @property string brandId
 * @property string name
 * @property string logo
 * @property string coverPhoto
 * @property string email
 * @property string emailSupport
 * @property string phone
 * @property string phoneSupport
 * @property string personality
 * @property string audience
 */
class BrandResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandModel $data
         */

        return [
            "id" => $data->id,
            "brandId" => $data->brand_id,
            "name" => $data->name,
            "logo" => $data->logo_url ? ViewSrc::gen($data->logo_url) : null,
            "coverPhoto" => $data->cover_photo_url ? ViewSrc::gen($data->cover_photo_url) : null,
            "email" => $data->email,
            "emailSupport" => $data->support_email,
            "phone" => $data->phone,
            "phoneSupport" => $data->support_phone,
            "personality" => $data->personality,
            "audience" => $data->audience,
        ];
    }
}