<?php

namespace Elevator\Brand\Resources;

use BrickLayer\Lay\Core\View\ViewSrc;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandTestimonyModel;

/**
 * @property string id
 * @property string name
 * @property string message
 * @property string company
 * @property string position
 * @property string dp
 */
class TestimonyResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandTestimonyModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "message" => $data->testimony,
            "company" => $data->company,
            "position" => $data->position,
            "dp" => $data->dp_src ? ViewSrc::gen($data->dp_src) : null,
        ];
    }
}