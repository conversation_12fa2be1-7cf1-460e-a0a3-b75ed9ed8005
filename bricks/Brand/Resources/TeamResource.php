<?php

namespace Elevator\Brand\Resources;

use Brick<PERSON>ayer\Lay\Core\View\ViewSrc;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Brand\Model\ElevatorBrandPartnerModel;
use Elevator\Brand\Model\ElevatorBrandTeamModel;

/**
 * @property string id
 * @property int order
 * @property string name
 * @property string slug
 * @property string dp
 * @property string position
 * @property string intro
 * @property string about
 * @property array social
 */
class TeamResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorBrandTeamModel $data
         */

        return [
            "id" => $data->id,
            "order" => $data->order,
            "name" => $data->name,
            "slug" => $data->slug,
            "dp" => $data->dp_src ? ViewSrc::gen($data->dp_src) : null,
            "position" => $data->position,
            "intro" => $data->intro,
            "about" => $data->about,
            "social" => $data->social,
        ];
    }
}