<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $map_id
 * @property string $location
 * @property int $entry_date
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorSitemapEntry extends BaseModelHelper
{
    public static string $table = "elevator_sitemap_entries";

    use CreatedBy;
    use HasFoundation;

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['id'],
            update_columns: ['updated_at', 'map_id', 'deleted'],
            action: 'UPDATE'
        );
    }

    public function new_entry(string $location, int $entry_date, string $map_id, ?string $client_id = null) : static
    {
        return $this->add([
            "location" => $location,
            "entry_date" => $entry_date,
            "map_id" => $map_id,
            "client_id" => $client_id ?? $this->client_id(),
        ], true);
    }

    public function by_sitemap(string $index) : array
    {
        return $this->pre_run(fn(SQL $db) => $db->sort("updated_at","desc"))
            ->all_by_col("map_id", $index);
    }

    public function by_location(string $location, ?string $client_id = null) : static
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $client_id ?? $this->client_id()))
            ->get_by("location", $location);
    }

}
