<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Model;

use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $channel_id
 * @property string $post_id
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorRssItem extends BaseModelHelper
{
    public static string $table = "elevator_rss_items";

    use CreatedBy;
    use HasFoundation;

    public function by_post(string $id) : static
    {
        return $this->get_by("post_id", $id);
    }

    public function by_channel(string $channel_id) : static
    {
        return $this->get_by("channel_id", $channel_id);
    }
}
