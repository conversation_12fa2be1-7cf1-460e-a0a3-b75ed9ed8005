<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use Elevator\Blog\Model\ElevatorPostCategory;
use Elevator\Blog\Model\ElevatorPostCollection;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $category_id
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorRssChannel extends BaseModelHelper
{
    public static string $table = "elevator_rss_channels";

    use CreatedBy;
    use HasFoundation;

    protected function category_table() : string
    {
        return ElevatorPostCategory::$table;
    }

    protected function collection_table() : string
    {
        return ElevatorPostCollection::$table;
    }

    public function add_new(string $category_id, int $entry_date, ?string $client_id = null) : static
    {
        return $this->add([
            "category_id" => $category_id,
            "created_at" => $entry_date,
            "updated_at" => $entry_date,
            "client_id" => $client_id ?? $this->client_id()
        ]);
    }

    public function by_category(string $category_id) : static
    {
        return $this->get_by("category_id", $category_id);
    }

    public function get_collection(string $channel_id) : array
    {
        $table = self::$table;
        $category = self::category_table();
        $collection = self::collection_table();

        return self::db()->column("$collection.*")->loop()->where("$table.id", $channel_id)
            ->join($category, "left")->on("$category.id", "$table.category_id")
            ->join($collection, "left")->on("$collection.id", "$category.collection")
            ->then_select();
    }

}
