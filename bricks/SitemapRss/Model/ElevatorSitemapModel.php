<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property int $year
 * @property int $month
 * @property int $week
 * @property string $timezone
 * @property string $utc_offset
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorSitemapModel extends BaseModelHelper
{
    public static string $table = "elevator_sitemaps";

    use CreatedBy;
    use HasFoundation;

    public function new_entry(int $entry_date, ?string $client_id = null) : self
    {
        $date = self::get_date_array($entry_date);

        return $this->add([
            "year" => $date['year'],
            "month" => $date['month'],
            "week" => $date['week'],
            "timezone" => $date['tz'],
            "utc_offset" => $date['utc'],
            "updated_at" => $entry_date,
            "client_id" => $client_id ?? $this->client_id()
        ]);
    }

    public function list_entries(?string $client_id = null) : array
    {
        return $this->pre_run(function (SQL $db){
            $db->sort("year","desc")
                ->sort("month","desc")
                ->sort("week","desc");
        })->all_by_col("client_id", $client_id ?? $this->client_id());
    }

    public function entry_by_date(int $timestamp_or_year, ?int $month = null, ?int $week = null, ?string $client_id = null) : static
    {
        $year = $timestamp_or_year;

        if(!$month || !$week) {
            $date = self::get_date_array($timestamp_or_year);
            $year = $date['year'];
            $month = $date['month'];
            $week = $date['week'];
        }

        return $this->pre_run(function (SQL $db) use ($month, $week) {
            $db->and_where("month","$month")
                ->and_where("week", "$week")
                ->and_where("client_id", $client_id ?? $this->client_id());
        })->get_by("year", "$year");
    }

    /**
     * @param int $unix_timestamp
     * @return array{
     *     year: int,
     *     month: int,
     *     week: int,
     *     tz: string, // timezone (e.g WAT)
     *     utc: string, // utc offset (e.g +01:00)
     * }
     */
    public static function get_date_array(int $unix_timestamp) : array
    {
        $date = explode(" ", LayDate::date($unix_timestamp, "Y m W P"));

        return [
            "year" => intval($date[0]),
            "month" => intval($date[1]),
            "week" => LayDate::week_of_month($unix_timestamp),
            "tz" => $date[2],
            "utc" => $date[2],
        ];
    }
}
