<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Controller;

use BrickLayer\Lay\Libs\LayDate;
use Elevator\SitemapRss\Model\ElevatorSitemapModel;
use Elevator\SitemapRss\Model\ElevatorSitemapEntry;
use Elevator\Utils\ElevatorController;

class ElevatorSitemapController extends ElevatorController
{
    public function model(): ElevatorSitemapModel
    {
        return new ElevatorSitemapModel();
    }

    public function entry_model(): ElevatorSitemapEntry
    {
        return new ElevatorSitemapEntry();
    }

    public function index_from_date(int $unix_timestamp) : ElevatorSitemapModel
    {
        return $this->model()->entry_by_date($unix_timestamp);
    }

    public function add_to_index(int $entry_date, string $url) : ElevatorSitemapEntry
    {
        $index = $this->index_from_date($entry_date);

        if($index->exists())
            return $this->entry_model()->new_entry($url, $entry_date, $index->id);

        $sitemap = $this->model();

        $index = $sitemap->new_entry($entry_date);

        return $this->entry_model()->new_entry($url, $entry_date, $index->id);
    }

    public function update_index(int $entry_date, string $url, bool $force_add_to_index = false) : ElevatorSitemapEntry
    {
        $entry = $this->entry_model()->by_location($url);

        if($force_add_to_index || $entry->is_empty())
            return $this->add_to_index($entry_date, $url);

        $sitemap = $this->model()->fill($entry->map_id);

        if($sitemap->is_empty())
            return $this->add_to_index($entry_date, $url);

        $date = $this->model()::get_date_array($entry_date);

        if(
            !(
                $sitemap->year == $date['year'] &&
                $sitemap->month == $date['month'] &&
                $sitemap->week == $date['week']
            )
        )
            $sitemap = $sitemap->new_entry($entry_date);

        return $this->entry_model()->new_entry($url, $entry_date, $sitemap->id);
    }

    public function get_sitemap_index() : string
    {
        $domain = static::foundation()->domain();
        $updated = LayDate::date(format_index: 0);
        $index = <<<IDX
          <sitemap>
            <loc>{$domain}sitemap.xml?static=1</loc>
            <lastmod>$updated</lastmod>
          </sitemap>\n
        IDX;

        $sitemap = $this->model();

        foreach ($sitemap->list_entries() as $map) {
            $map = $sitemap->fill($map);

            $updated = LayDate::date($map->updated_at, format_index: 0);
            $index .= <<<IDX
              <sitemap>
                <loc>{$domain}sitemap.xml?year={$map->year}&amp;month={$map->month}&amp;week={$map->week}</loc>
                <lastmod>$updated</lastmod>
              </sitemap>\n
            IDX;
        }

        $index = rtrim($index, "\n");

        return <<<MAP
        <?xml version="1.0" encoding="UTF-8"?>
        <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        $index
        </sitemapindex>
        MAP;
    }

    public function get_sitemap_from_date(
        ?string $year,
        ?string $month,
        ?string $week,
        string $loc_prefix = "/"
    ) : ?string
    {
        if(empty($year) || empty($month) || empty($week))
            return null;

        $sitemap = $this->model()->entry_by_date((int) $year, (int) $month, (int) $week);

        if($sitemap->is_empty())
            return null;

        $maps = $this->entry_model()->by_sitemap($sitemap->id);

        $domain = static::foundation()->domain();
        $article_sections = "";

        foreach($maps as $map) {
            $updated = LayDate::date($map['updated_at'] ?? $map['created_at'], "Y-m-d\TH:i:s.000P");
            $article_sections .= <<<SEC
            <url>
                <loc>{$domain}{$loc_prefix}{$map['location']}</loc>
                <lastmod>$updated</lastmod>
            </url>\n
            SEC;
        }

        $article_sections = rtrim($article_sections, "\n");

        return <<<MAP
        <?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            $article_sections
        </urlset>
        MAP;
    }

    /**
     * @param array<int, string>|null $maps
     * @return string
     */
    public function static_sitemaps(?array $maps = []) : string
    {
        $domain = static::foundation()->domain();
        $today = LayDate::date(format: "Y-m-d\TH:i:s.000P");

        $mapped_urls = "";

        foreach($maps as $map){
            $updated = $today;
            $mapped_urls .= <<<SEC
            <url>
                <loc>{$domain}$map</loc>
                <lastmod>$updated</lastmod>
            </url>\n
            SEC;
        }

        return <<<MAP
        <?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            $mapped_urls
        </urlset>
        MAP;
    }

    public function remove_entry(string $slug) : array
    {
        $entry = $this->entry_model()->by_location(self::clean($slug));

        if($entry->is_empty())
            return self::res_warning("ElevatorSitemapModel entry does not exist, it may have been removed already");

        $entry->delete_self();

        return self::res_success("ElevatorSitemapModel entry for location[$entry->location] deleted successfully");
    }
}
