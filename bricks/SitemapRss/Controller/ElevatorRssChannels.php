<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Controller;

use BrickLayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use BrickLayer\Lay\Libs\String\Escape;
use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Blog\Resource\PostResource;
use Elevator\SitemapRss\Model\ElevatorRssChannel;
use Elevator\SitemapRss\Model\ElevatorRssItem;
use Elevator\SitemapRss\Model\ElevatorSitemapModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\ElevatorFoundation;

class ElevatorRssChannels extends ElevatorController
{
    public function model(): ElevatorRssChannel
    {
        return new ElevatorRssChannel();
    }

    public function item_model(): ElevatorRssItem
    {
        return new ElevatorRssItem();
    }

    private function post_controller() : ElevatorPostController
    {
        return ElevatorFoundation::new()->post_ctrl();
    }

    public function update_channel_and_item(string $category_id, string $post_id, int $entry_date) : array
    {
        $update_channel = true;

        self::cleanse($category_id);

        $channel = $this->model();
        $channel->by_category($category_id);

        if($channel->is_empty()) {
            $update_channel = false;
            $channel->add_new($category_id, $entry_date);
        }

        if($update_channel)
            $channel->edit_self([
                "updated_at" => $entry_date,
            ]);

        if($channel->is_empty())
            return self::res_warning("Could not update RSS Channel at the moment, please try again later");

        $item = $this->new_item($channel->id, $post_id, $entry_date);

        if($item->is_empty())
            return self::res_warning("Could not update RSS Item at the moment, please try again later");

        return self::res_success("Rss channel and item updated successfully");
    }

    public function new_item(string $channel_id, string $post_id, int $entry_date) : ElevatorRssItem
    {
        self::cleanse($channel_id);
        self::cleanse($post_id);

        $rss = $this->item_model();
        $rss->by_post($post_id);

        $cols = [
            "created_at" => $entry_date,
            "channel_id" => $channel_id,
            "post_id" => $post_id,
            "deleted" => '0',
            "deleted_at" => null,
            "deleted_by" => null,
        ];

        if($rss->exists()) {
            $rss->edit_self($cols);

            return $rss;
        }

        return $rss->add($cols);
    }


    public function list_items(int $page = 1) : array
    {
        return RssResource::collect(
            $this->item_model()->all($page)
        );
    }

    public function items_by_collection(string $collection_slug) : array
    {
        return $this->post_controller()->by_collection($collection_slug);
    }

    public function remove_entry(string $post_id) : array
    {
        $rss = $this->item_model()->by_post(self::clean($post_id));

        if($rss->is_empty() || !$rss->delete_self())
            return self::res_warning( "Could not delete rss feed! It could have been deleted already");

        return self::res_success( "RSS Feed entry deleted successfully!");
    }

    /**
     * @param array{
     *     title: string,
     *     desc: string,
     *     url: string,
     *     updated_at: int,
     * } $channel
     * @param array<int, PostResource> $items
     * @param string $slug_prefix
     * @return string
     */
    public static function template(array $channel, array $items, string $slug_prefix = "") : string
    {
        $item_body = "";
        $domain = static::foundation()->domain();
        $site_author = static::foundation()->site_author();
        $year = date("Y");
        $updated_at = LayDate::date($channel['updated_at'], "D, d M Y H:i:s P");
        $escape = fn($v) => Escape::clean($v, [EscapeType::P_SPEC_CHAR, EscapeType::P_REPLACE], [
            "strict" => false,
            "special_chars" => [
                "flag" => ENT_QUOTES|ENT_SUBSTITUTE,
            ],
            "find" => ["&amp;#", "&amp;amp;"],
            "replace" => ["&#", "&amp;"],
        ]);

        foreach ($items as $item) {
            $date = LayDate::date($item->updatedAt, "D, d M Y H:i:s P");

            $item->update("title", $escape($item->title));
            $item->update("desc", $escape($item->desc));
            $item->update("category", $escape($item->category));
            $item->update("author", $escape($item->author));
            $item->update("tags", $escape($item->tags));

            $slug = $slug_prefix . $item->slug;

            $item_body .= <<<ITEM
            <item>
            <title>$item->title</title>
            <link>{$domain}{$slug}</link>
            <guid isPermaLink="false">$item->id</guid>
            <pubDate>$date</pubDate>
            <media:content/>
            <description>$item->desc</description>
            <category>$item->collection</category>
            <category>$item->collection / $item->category</category>
            <media:keywords>$item->tags</media:keywords>
            <dc:creator>$item->author</dc:creator>
            <dc:publisher>$site_author</dc:publisher>
            <media:thumbnail url="$item->coverPhoto" width="{$item->coverPhotoRatio['width']}" height="{$item->coverPhotoRatio['height']}"/>
            </item>
            ITEM;

        }

        $title = $escape($channel['title']);
        $desc = $escape($channel['desc']);

        $domain = rtrim($domain, "/");
        return /** @lang text */ <<<CHANNEL
        <?xml version="1.0" encoding="utf-8"?>
        <rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:media="http://search.yahoo.com/mrss/" version="2.0">
        <channel>
        <title>$title</title>
        <description>$desc</description>
        <link>$domain</link>
        <atom:link href="$domain/{$channel['url']}" rel="self" type="application/atom+xml"/>
        <copyright>&#169; $site_author $year</copyright>
        <language>en-US</language>
        <lastBuildDate>$updated_at</lastBuildDate>
        $item_body
        </channel>
        </rss>
        CHANNEL;
    }

}
