<?php

namespace Elevator\SitemapRss\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;

class SitemapEntriesResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        return [
            "id" => $data->id,
            "name" => $data->name,
            "slug" => $data->slug,
            "desc" => $data->description,
            "collection" => $data->collection->name,
            "canDelete" => !$data->is_in_use,
            "date" => $data->updated_at ?? $data->created_at,
        ];
    }
}