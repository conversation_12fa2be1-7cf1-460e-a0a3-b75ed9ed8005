<?php
declare(strict_types=1);

namespace Elevator\SitemapRss\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;

class SitemapResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        return [
            "id" => $data->id,
            "dateCreated" => $data->created_at,
            "dateUpdated" => $data->updated_at ?? null,
        ];
    }
}
