<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use Elevator\Blog\Model\ElevatorPostTag;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPostTags extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Post;
    }

    public function model(): ElevatorPostTag
    {
        return new ElevatorPostTag();
    }
}