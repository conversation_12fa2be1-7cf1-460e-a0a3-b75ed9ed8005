<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use BrickLayer\Lay\Core\Api\Enums\ApiStatus;
use BrickLayer\Lay\Libs\LayArray;
use BrickLayer\Lay\Libs\LayCSV;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Blog\Enums\PostConversionGoal;
use Elevator\Blog\Enums\PostKpis;
use Elevator\Blog\Enums\PostSearchIntent;
use Elevator\Blog\Enums\PostSocialChannels;
use Elevator\Blog\Enums\PostTypes;
use Elevator\Blog\Model\ElevatorPostCalendar;
use Elevator\Blog\Request\AssignAuthorRequest;
use Elevator\Blog\Request\CalendarRequestCreate;
use Elevator\Blog\Request\CalendarRequestEdit;
use Elevator\Blog\Resource\PostCalendarResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPostCalendarController extends ElevatorController implements HasPermission, HasLog
{
    /**
     * @abstract  You can overwrite to use your own
     */
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::PostCalendar;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorPostCalendar
    {
        return new ElevatorPostCalendar();
    }

    public function list(int $page = 1, int $limit = 100): array
    {
        $range = self::request(false)->range ?? null;

        $start_date = null;
        $end_date = null;

        if($range) {
            $range = explode(" to ", $range);

            $start_date = $range[0];
            $end_date = $range[1];
        }

        return PostCalendarResource::collect(
            $this->model()->list([
                "page" => $page,
                "length" => $limit,
                "start_date" => $start_date,
                "end_date" => $end_date,
            ])
        );
    }

    public function assign_author(): array
    {
        $request = new AssignAuthorRequest();

        if($request->error)
            return self::res_warning($request->error);

        $user = static::foundation()->user_profile()->model()->fill($request->author);
        $calendar = $this->model()->fill($request->calendar_id);

        $edited = $calendar->edit_self([
            "author" => $user->id
        ]);

        if (!$edited)
            return self::res_warning("Could not edit calendar at the moment, please try again later");

        if(SQL::new()->query_info['has_data']) {
            if($request->notify_author)
                static::foundation()->mailer()->notify_author_calendar_assignment([
                    "name" => $user->concat_name(),
                    "email" => $user->email,
                    "calendar_id" => $calendar->id
                ]);

            $this->activity_log(
                LogActivityTypes::UPDATE,
                "A new author [$user->profile_id] was assigned to calendar entry [$calendar->id]",
            );
        }

        return self::res_success("Author $user->first_name was successfully assigned $calendar->post_title");
    }

    /**
     * @return array<int, PostCalendarResource>
     */
    public function today_unassigned() : array
    {
        $data = $this->model()->not_started_today(LayDate::date(format: "Y-m-d"));

        return PostCalendarResource::collect($data, as_array: false);
    }

    public function by_id(string $id) : ?PostCalendarResource
    {
        $data = $this->model()->fill(self::clean($id));

        if($data->is_empty()) return null;

        return new PostCalendarResource($data);
    }

    public function add(): array
    {
        $request = new CalendarRequestCreate();

        if($request->error)
            return self::res_warning($request->error);

        $added = $this->model()->batch($request);

        if (!$added)
            return self::res_warning("Could not create calendar entrie(s) at the moment");

        $this->activity_log(
            LogActivityTypes::CREATE,
            count($request->props()) . " Post Calendar entries was created",
        );

        return self::res_success("Calendar entries created successfully");
    }

    public function add_via_csv(): array
    {
        $file = $_FILES['file']['tmp_name'] ?? null;

        if(!$file)
            return self::res_warning("Failed to process bulk calendar: File not received!");

        $cols = [];
        $error_msg = "";

        $cat_model = static::foundation()->post_category()->model();

        $csv = LayCSV::process($file, function ($row, $head) use (&$cols, &$error_msg, $cat_model) {
            $categories = LayArray::map(explode("\n", $head['Categories']), function($v) use (&$error_msg, $cat_model) {
                $v = self::clean($v);

                if($cat_model->fill($v)->is_empty()) $error_msg .= "Category ID: $v is not valid\n";

                return $v;
            });

            $goals = LayArray::map(explode("\n", $head['Goals']), function($v) use (&$error_msg) {
                $v = trim($v);

                if(!PostConversionGoal::is_enum($v)) $error_msg .= "Goal: $v is not valid\n";

                return trim($v);
            });

            $intents = LayArray::map(explode("\n", $head['Search Intents']), function($v) use (&$error_msg) {
                $v = trim($v);

                if(!PostSearchIntent::is_enum($v)) $error_msg .= "Search Intent: $v is not valid\n";

                return trim($v);
            });

            $social = LayArray::map(explode("\n", $head['Social Channels']), function($v) use (&$error_msg) {
                $v = trim($v);

                if(!PostSocialChannels::is_enum($v)) $error_msg .= "Social Channel: $v is not valid\n";

                return trim($v);
            });

            $kpis = LayArray::map(explode("\n", $head['KPIs']), function($v) use (&$error_msg) {
                $v = trim($v);

                if(!PostKpis::is_enum($v)) $error_msg .= "KPI: $v is not valid\n";

                return trim($v);
            });

            $post_type = trim($head['Post Type']);
            if(!PostTypes::is_enum($post_type))
                $error_msg .= "Post Type: $post_type is not valid\n";

            $author = !empty($head['Author']) ? self::clean($head['Author']) : null;
            if($author && static::foundation()->user_profile()->model()->by_auth_id($author)->is_empty())
                $error_msg .= "Author ID: $author is not valid\n";

            $date = trim($head["Publish Date"]);
            if(!LayDate::is_valid($date))
                $error_msg .= "Publish Date: $date is not valid\n";

            $cols[] = [
                "publish_date" => $date,
                "author" => $author,
                "post_title" => $head["Post Title"],
                "post_categories" => json_encode($categories),
                "post_summary" => $head["Post Summary"],
                "post_type" => $post_type,
                "audience" => $head["Target Audience"],
                "goals" => json_encode($goals),
                "search_intents" => json_encode($intents),
                "social_channels" => json_encode($social),
                "kpis" => json_encode($kpis),
                "pry_keyword" => $head["Primary Keyword"],
                "sec_keywords" => json_encode(explode("\n", $head["Secondary Keywords"])),
                "word_count" => $head['Word Count'],
                "cta" => $head['CTA'],
                "internal_links" => json_encode(explode("\n", $head["Internal Links"])),
                "external_links" => json_encode(explode("\n", $head["External Links"])),
                "assets_needed" => json_encode(explode("\n", $head["Assets Needed"])),
            ];
        }, 1024);

        if(!ApiStatus::is_ok($csv['code']))
            return $csv;

        if(!empty($error_msg))
            return self::res_warning($error_msg);

        $calendar = $this->model();

        $added = $calendar->batch($cols);

        if (!$added)
            return self::res_warning("Could not upload your calendar entries at the moment, please try again later");

        if(SQL::new()->query_info['has_data'])
            $this->activity_log(
                LogActivityTypes::CREATE,
                count($cols) . " Post Calendar entries was created via bulk upload",
            );

        return self::res_success("Calendar entries created successfully");
    }

    public function edit(): array
    {
        $request = new CalendarRequestEdit();

        if($request->error)
            return self::res_warning($request->error);

        $edited = $this->model()->edit_batch($request, "id", "calendar_id");

        if (!$edited)
            return self::res_warning("Could not edit calendar at the moment, please try again later");

        if(SQL::new()->query_info['has_data'])
            $this->activity_log(
                LogActivityTypes::UPDATE,
                count($request->props()) . " Post Calendar entries was updated",
            );

        return self::res_success("Calendar entries updated successfully");
    }

    public function delete(): array
    {
        $id = self::clean(self::request()->id);

        $post = $this->model()->fill($id);

        if($post->is_empty())
            return self::res_success("Calendar entry does not exist");

        $post->delete_self();

        $this->activity_log(
            LogActivityTypes::DELETE,
            "Calendar entry [$post->id] with title [$post->post_title] was deleted",
        );

        return self::res_success(
            "Calendar entry deleted successfully!"
        );
    }

    public function delete_bulk(): array
    {
        $bulk_id = self::request(false)->bulk_id;

        if(empty($bulk_id))
            return self::res_warning("Entry ids were not received!");

        $act_by = static::foundation()->user_session()::current_user();
        $now = LayDate::now();

        $request = [];

        foreach (explode(",", $bulk_id) as $id) {
            $request[] = [
                "id" => $id,
                "deleted" => "1",
                "deleted_by" => $act_by,
                "deleted_at" => $now,
            ];
        }

        $deleted = $this->model()->edit_batch($request, "id");

        if(!$deleted)
            return self::res_warning("Could not delete entries at the moment, please try again later!");

        $total = count($request);

        $this->activity_log(
            LogActivityTypes::DELETE,
            $total . " calendar entries deleted",
        );

        return self::res_success("Calendar entries deleted successfully!");
    }

}