<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use Elevator\Blog\Model\ElevatorPostCategory;
use Elevator\Blog\Request\CategoryRequestCreate;
use Elevator\Blog\Request\CategoryRequestEdit;
use Elevator\Blog\Resource\PostCategoryResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPostCategories extends ElevatorController implements HasPermission, HasLog
{
    /**
     * @abstract  You can overwrite to use your own
     */
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::PostCategory;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorPostCategory
    {
        return new ElevatorPostCategory();
    }

    public function add(): array
    {
        $request = new CategoryRequestCreate();

        if($request->error)
            return self::res_warning($request->error);

        $category = $this->model();

        if ($category->is_duplicate($request))
            return self::res_warning("Category exists already!");

        $added = $category->add($request, true)->exists();

        if (!$added)
            return self::res_warning("Could not create category at the moment");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "Category [$category->id] with name [$category->name] was created",
        );

        return self::res_success("Category [$request->name] created successfully");
    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return PostCategoryResource::collect(
            $this->model()->all_enabled($page, $limit),
            as_array: $as_array
        );
    }

    public function edit(): array
    {
        $request = new CategoryRequestEdit();

        if($request->error)
            return self::res_warning($request->error);

        $category = $this->model()->fill($request->id);

        if($category->is_empty())
            return self::res_warning("Category does not exist, please try again later");

        $edited = $category->edit_self($request);

        if (!$edited)
            return self::res_warning("Could not edit category at the moment, please try again later");

        $category = $category->refresh();

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Category [$category->id] was updated",
        );

        return self::res_success("Category [$category->name] was updated successfully");
    }

    public function delete(): array
    {
        $id = self::clean(self::request()->id);

        $post = $this->model()->$id;

        if($post->is_empty())
            return self::res_success("Category does not exist");

        $post->edit_self([ 'disabled' => 1 ]);

        $this->activity_log(
            LogActivityTypes::DELETE,
            "Category [$post->id] with name [$post->name] was disabled 'DELETED'",
        );

        return self::res_success(
            "Category disabled successfully! If it has been used, it'll still reflect, 
            but it won't show up on subsequent lists"
        );
    }
}