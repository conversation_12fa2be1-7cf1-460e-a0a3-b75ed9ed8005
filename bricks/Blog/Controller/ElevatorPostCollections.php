<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\Blog\Model\ElevatorPostCollection;
use Elevator\Blog\Request\CollectionRequestCreate;
use Elevator\Blog\Request\CollectionRequestEdit;
use Elevator\Blog\Resource\PostCollectionResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPostCollections extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::PostCategory;
    }

    public function model(): ElevatorPostCollection
    {
        return new ElevatorPostCollection();
    }

    public function by_slug(string $slug) : ?PostCollectionResource
    {
        $data = $this->model()->get_by("slug", self::clean($slug));

        if($data->is_empty())
            return null;

        return new PostCollectionResource($data);
    }

    public function list(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return PostCollectionResource::collect(
            $this->model()
                ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
                ->all_by_col("disabled", '0'),
            as_array: $as_array
        );
    }

    public function add(): array
    {
        $request = new CollectionRequestCreate();

        if($request->error)
            return self::res_warning($request->error);

        $collection = $this->model();

        if ($collection->is_duplicate($request))
            return self::res_warning("Collection exists already!");

        $added = $collection->add($request, true)->exists();

        if (!$added)
            return self::res_warning("Could not create collection [$request->name] at the moment");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "Collection [$collection->id] with name [$collection->name] was created",
        );

        return self::res_success("Collection [$request->name] created successfully");
    }

    public function edit(): array
    {
        $request = new CollectionRequestEdit();

        if($request->error)
            return self::res_warning($request->error);

        $category = $this->model()->fill($request->id);

        if($category->is_empty())
            return self::res_warning("Collection does not exist, please try again later");

        $edited = $category->edit_self($request);

        if (!$edited)
            return self::res_warning("Could not edit collection at the moment, please try again later");

        $category = $category->refresh();

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Collection [$category->id] was updated",
        );

        return self::res_success("Collection [$category->name] was updated successfully");
    }

    public function delete(): array
    {
        $id = self::clean(self::request()->id);

        $post = $this->model()->fill($id);

        if($post->is_empty())
            return self::res_success("Collection does not exist");

        $post->edit_self([ 'disabled' => 1 ]);

        $this->activity_log(
            LogActivityTypes::DELETE,
            "Collection [$post->id] with name [$post->name] was disabled 'DELETED'",
        );

        return self::res_success(
            "Collection disabled successfully! If it has been used, it'll still reflect, 
            but it won't show up on subsequent lists"
        );
    }

}