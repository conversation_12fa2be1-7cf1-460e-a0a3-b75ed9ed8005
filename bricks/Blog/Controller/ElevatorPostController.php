<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use BrickLayer\Lay\Core\View\Tags\Anchor;
use Elevator\Blog\Model\ElevatorPostCalendar;
use Elevator\Blog\Model\ElevatorPostModel;
use Elevator\Blog\Model\ElevatorPostTag;
use Elevator\Blog\Resource\PostResource;
use Elevator\Blog\Traits\CreateEditDelete;
use Elevator\Blog\Traits\ListVariants;
use Elevator\SitemapRss\Controller\ElevatorRssChannels;
use Elevator\SitemapRss\Controller\ElevatorSitemapController;
use Elevator\User\Model\ElevatorProfileModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Enums\PermissionAccessType;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\PermissionManager;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPostController extends ElevatorController implements HasPermission, HasLog
{
    use CreateEditDelete, ListVariants;

    use ActivityLog;

    public function store() : string
    {
        return "post-resources" . DIRECTORY_SEPARATOR;
    }

    public static function publish_id() : PermissionInterface
    {
        return ModulesEnum::PostPublish;
    }

    public static function revision_id() : PermissionInterface
    {
        return ModulesEnum::PostApprovalRevision;
    }

    public static function module_id() : PermissionInterface
    {
        return ModulesEnum::Post;
    }

    public function model(): ElevatorPostModel
    {
        return new ElevatorPostModel();
    }

    private function tag_model(): ElevatorPostTag
    {
        return static::foundation()->post_tag()->model();
    }

    private function sitemap_ctrl(): ElevatorSitemapController
    {
        return static::foundation()->sitemap_ctrl();
    }

    private function rss_ctrl(): ElevatorRssChannels
    {
        return static::foundation()->rss_ctrl();
    }

    private function calendar_model(): ElevatorPostCalendar
    {
        return static::foundation()->post_calendar()->model();
    }

    private function profile_model(): ElevatorProfileModel
    {
        return static::foundation()->user_profile()->model();
    }

    /**
     * Basically your user model, but return the values of the user in the specified array format
     *
     * @param string $created_by
     * @return null|array{
     *     id: string,
     *     dp: string,
     *     name: string,
     *     email: string,
     *     slug: string,
     *     about: string,
     * }
     */
    public function post_author(string $created_by) : ?array
    {
        $me = $this->profile_model()->fill($created_by);

        return [
            "id" => $me->id,
            "dp" => $me->dp,
            "name" => $me->concat_name(),
            "email" => $me->email,
            "slug" => $me->slug,
            "about" => $me->about,
        ];
    }

    /**
     * Other users who have access to the post returned in a 2d array in the format specified
     *
     * @return null|array<int, array{
     *     name: string,
     *     email: string,
     * }>
     */
    public function post_editors() : ?array
    {
        return $this->profile_model()
            ->each(fn(ElevatorProfileModel $data) => [
                "name" => $data->first_name . " " . $data->last_name,
                "email" => $data->email,
            ])
            ->users_by_permission( [PermissionManager::access_obj(static::publish_id())['access']['ALL']] );
    }

    public function user_can_publish_post() : bool
    {
        return static::foundation()->user_role()::permitted(static::publish_id(), PermissionAccessType::CREATE);
    }

    public function get_writers(bool $include_current_author = true) : array
    {
        return $this->profile_model()
            ->each(fn(ElevatorProfileModel $data) => [
                "name" => $data->first_name . " " . $data->last_name,
                "id" => $data->id,
            ])
            ->users_by_permission(
                [
                    PermissionManager::access_obj(static::module_id())['access']['ALL'],
                    PermissionManager::access_obj(static::module_id())['access']['CREATE'],
                ],
                include_current_user: $include_current_author
            );
    }


    /**
     * The link that should be displayed in the email sent to editors and author when required
     * @abstract Overwrite if needed
     * @param string $post_id
     * @return string
     */
    public function update_post_link(string $post_id) : string {
        return Anchor::new()->href("blog/compose/$post_id")->get_href();
    }


    public function by_collection(string $id_or_slug, int $page = 1, int $limit = 4, bool $as_array = true) : array
    {
        return PostResource::collect(
            $this->model()->all_by_collection(self::clean($id_or_slug), $page, $limit),
            as_array: $as_array
        );
    }

    public function by_category(string $id_or_slug, int $page = 1, int $limit = 4, bool $as_array = true) : array
    {
        return PostResource::collect(
            $this->model()->all_by_category(self::clean($id_or_slug), $page, $limit),
            as_array: $as_array
        );
    }

    public function by_author(string $id_or_slug, int $page = 1, int $limit = 4, bool $as_array = true) : array
    {
        return PostResource::collect(
            $this->model()->all_by_author(self::clean($id_or_slug), $page, $limit),
            as_array: $as_array
        );
    }

}
