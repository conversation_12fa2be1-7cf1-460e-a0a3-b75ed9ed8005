<?php
declare(strict_types=1);

namespace Elevator\Blog\Controller;

use <PERSON><PERSON>ayer\Lay\Core\LayConfig;
use <PERSON>Layer\Lay\Libs\LayCookieStorage;
use BrickLayer\Lay\Libs\LayFn;
use Elevator\Blog\Enums\PostReactionType;
use Elevator\Blog\Model\ElevatorPostReaction;
use Elevator\Blog\Request\RecordViewRequest;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorPostReactions extends ElevatorController
{
    private static string $cache_key;

    use HasFoundation;

    public function model(): ElevatorPostReaction
    {
        return new ElevatorPostReaction();
    }

    protected function ignore_admin() : bool
    {
        return (bool) static::foundation()->user_session()::validate_session(false);
    }

    protected static function cache_key() : string
    {
        if(isset(self::$cache_key)) return self::$cache_key;

        return self::$cache_key = LayFn::env("POST_REACT_KEY", substr(LayConfig::site_data()->name->short, 0, 4)) . "_rct";
    }

    protected function get_cache() : array
    {
        $key = self::cache_key();

        if(!isset($_COOKIE[$key], $_SESSION[$key]))
            return [];

        $data = $_COOKIE[$key] ?? $_SESSION[$key];

        return json_decode($data, true);
    }

    protected function counted_already(string $post_id, PostReactionType $type) : bool
    {
        $key = self::cache_key();
        $cache = $this->get_cache();
        $_SESSION[$key] = $cache;

        if(empty($cache) && empty($_SESSION[$key]))
            return false;

        return in_array($post_id . "." . $type->name, $cache) || in_array($post_id . "." . $type->name, $_SESSION[$key]);
    }

    protected function cache_count(string $post_id, PostReactionType $type) : void
    {
        $key = self::cache_key();
        $cache = $this->get_cache();

        $cache[] = $post_id . "." . $type->name;

        $_SESSION[$key] = $cache;

        LayCookieStorage::save(
            $key,
            json_encode($cache),
            "1 day"
        );
    }

    protected function count_reaction(string $post_id, PostReactionType $type) : array
    {
        $type_name = $type->name;

        if($this->ignore_admin())
            return self::res_success("Ignoring $type_name count for admin user");

        if(LayConfig::is_bot())
            return self::res_success("Ignoring $type_name count for bots");

        if($this->counted_already($post_id, $type))
            return self::res_success("Recorded $type_name already!");

        $post = self::foundation()->post_ctrl()->model()->fill($post_id);

        if($post->is_empty())
            return self::res_warning("Blog post is invalid!");

        $imp = $this->model()->by_post_id($post->id);

        if($imp->is_empty()) {
            $imp->new_reaction($post_id, $type);

            if($imp->is_empty())
                return self::res_warning("Could not count $type_name at the moment, please try again later");

            $this->cache_count($post_id, $type);
            return self::res_success("$type_name counted successfully");
        }

        if(!$imp->update_reaction($type))
            return self::res_warning("Could not count $type_name at the moment, please try again later");

        $this->cache_count($post_id, $type);
        return self::res_success("$type_name counted successfully");
    }

    public function count_impression(string $post_id) : array
    {
        return $this->count_reaction($post_id, PostReactionType::IMPRESSION);
    }

    public function record_view() : array
    {
        $request = new RecordViewRequest();

        if($request->error)
            return self::res_warning($request->error);

        return $this->count_reaction($request->post_id, PostReactionType::VIEW);
    }

    public function record_share() : array
    {
        $request = new RecordViewRequest();

        if($request->error)
            return self::res_warning($request->error);

        return $this->count_reaction($request->post_id, PostReactionType::SHARE);
    }

}