<?php

namespace Elevator\Blog;

use Elevator\Blog\Controller\ElevatorPostCalendarController;
use Elevator\Blog\Controller\ElevatorPostCategories;
use Elevator\Blog\Controller\ElevatorPostCollections;
use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Blog\Controller\ElevatorPostReactions;
use Elevator\Blog\Enums\PostConversionGoal;
use Elevator\Blog\Enums\PostKpis;
use Elevator\Blog\Enums\PostSearchIntent;
use Elevator\Blog\Enums\PostSocialChannels;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Enums\PostTypes;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Enums\PermissionAccessType;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorPostController $posts;
    protected static ElevatorPostCollections $collection;
    protected static ElevatorPostCategories $category;
    protected static ElevatorPostCalendarController $calendar;
    protected static ElevatorPostReactions $reaction;

    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix . "/blog");
    }

    protected function pre_hook() : void
    {
        $foundation = self::foundation();

        self::$posts = $foundation->post_ctrl();
        self::$collection = $foundation->post_collection();
        self::$category = $foundation->post_category();
        self::$calendar = $foundation->post_calendar();
        self::$reaction = $foundation->post_reaction();
    }

    protected function hooks(): void
    {
        $this->primary_prefix();

        $this->group("reaction", function (){
            $this->put('impression/{post_id}')->bind(fn($id) => static::$reaction->count_impression($id));
            $this->put('view')->bind(fn() => static::$reaction->record_view());
            $this->put('share')->bind(fn() => static::$reaction->record_share());
        });

        $this->elevator_middleware()->is_auth();

        $this->post("delete")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts, PermissionAccessType::DELETE))
            ->bind(fn() => static::$posts->delete());

        $this->get("drafts")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts))
            ->bind(fn() => static::$posts->drafts());

        $this->get("published")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts))
            ->bind(fn() => static::$posts->published());

        $this->get("scheduled")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts))
            ->bind(fn() => static::$posts->scheduled());

        $this->get("awaiting")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts))
            ->bind(fn() => static::$posts->approval_requested());

        $this->get("improvement")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts::revision_id()))
            ->bind(fn() => static::$posts->revision());

        $this->get("search")->bind(fn() => static::$posts->search($_GET['query']));

        $this->group("utils", function () {
            $this->group_middleware($this->elevator_middleware()->is_permitted(static::$posts));

            $this->get("status-types/")->bind(fn() => PostStatus::render(true), [ "max_age" => "2 days" ]);
            $this->get("goals/")->bind(fn() => PostConversionGoal::cases_assoc(), [ "max_age" => "2 days" ]);
            $this->get("kpis/")->bind(fn() => PostKpis::cases_assoc(), [ "max_age" => "2 days" ]);
            $this->get("post-types/")->bind(fn() => PostTypes::cases_assoc(), [ "max_age" => "2 days" ]);
            $this->get("intents/")->bind(fn() => PostSearchIntent::cases_assoc(), [ "max_age" => "2 days" ]);
            $this->get("social-channels/")->bind(fn() => PostSocialChannels::cases_assoc(), [ "max_age" => "2 days" ]);
            $this->get("collaborators/")->bind(fn() => static::$posts->get_writers(false));
            $this->get("writers/")->bind(fn() => static::$posts->get_writers());
        });

        $this->group("category", function () {
            $this->get("list")->bind(fn() => static::$category->list());

            $this->post("new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$category, PermissionAccessType::CREATE))
                ->bind(fn() => static::$category->add());

            $this->post("edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$category, PermissionAccessType::EDIT))
                ->bind(fn() => static::$category->edit());

            $this->post("delete")
                ->middleware($this->elevator_middleware()->is_permitted(static::$category, PermissionAccessType::DELETE))
                ->bind(fn() => static::$category->delete());
        });


        $this->group("calendar", function () {
            $this->get("/{page}")->name("get-post-calendar-list")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar))
                ->bind(fn($page) => static::$calendar->list($page));

            $this->post("/")->name("create-new-calendar")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::CREATE))
                ->bind(fn() => static::$calendar->add());

            $this->post("/bulk/")->name("create-new-calendar-bulk")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::CREATE))
                ->bind(fn() => static::$calendar->add_via_csv());

            $this->post("edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::EDIT))
                ->bind(fn() => static::$calendar->edit());

            $this->post("assign-author")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::EDIT))
                ->bind(fn() => static::$calendar->assign_author());

            $this->post("delete")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::DELETE))
                ->bind(fn() => static::$calendar->delete());

            $this->post("delete-bulk")
                ->middleware($this->elevator_middleware()->is_permitted(static::$calendar, PermissionAccessType::DELETE))
                ->bind(fn() => static::$calendar->delete_bulk());

        });


        $this->group("collection", function () {
            $this->get("list")
                ->middleware($this->elevator_middleware()->is_permitted(static::$collection))
                ->bind(fn() => static::$collection->list());

            $this->post("new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$collection, PermissionAccessType::CREATE))
                ->bind(fn() => static::$collection->add());

            $this->post("edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$collection, PermissionAccessType::EDIT))
                ->bind(fn() => static::$collection->edit());

            $this->post("delete")
                ->middleware($this->elevator_middleware()->is_permitted(static::$collection, PermissionAccessType::DELETE))
                ->bind(fn() => static::$collection->delete());
        });

        $this->post("compose/new")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts, PermissionAccessType::CREATE))
            ->bind(fn() => static::$posts->add());

        $this->post("compose/autosave")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts, PermissionAccessType::CREATE))
            ->bind(fn() => static::$posts->auto_save());

        $this->post("compose/edit")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts, PermissionAccessType::EDIT))
            ->bind(fn() => static::$posts->edit());

        $this->post("compose/upload-file")
            ->middleware($this->elevator_middleware()->is_permitted(static::$posts, PermissionAccessType::CREATE))
            ->bind(fn() => static::$posts->upload_file_on_text_editor());

    }
}