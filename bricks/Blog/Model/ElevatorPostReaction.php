<?php
declare(strict_types=1);

namespace Elevator\Blog\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use <PERSON>Layer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Blog\Enums\PostReactionType;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $post_id
 * @property array<PostReactionType, int> $data_total
 * @property array<PostReactionType, int> $data_today
 * @property array<PostReactionType, int> $data_week
 * @property int $today_ends
 * @property int $week_ends
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorPostReaction extends BaseModelHelper
{
    public static string $table = "elevator_post_reactions";
    protected static string $week_starts = "sunday";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $columns['client_id'] ?? $this->client_id()))
            ->count("name", $columns['name']) > 0;
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['post_id'],
            update_columns: ['deleted'],
            action: 'REPLACE'
        );
    }

    private function reaction_struct(array $impression_type = []) : array
    {
        $default = [];

        foreach (PostReactionType::cases() as $react) {
            $default[$react->name] = 0;
        }

        return array_merge($default, $impression_type);
    }

    public function by_post_id(string $post_id) : static
    {
        return $this->get_by("post_id", $post_id);
    }

    public function new_reaction(string $post_id, PostReactionType $type) : static
    {
        $data = json_encode($this->reaction_struct([ $type->name => 1 ]));

        return $this->add([
            "post_id" => $post_id,
            "data_total" => $data,
            "data_today" => $data,
            "data_week" => $data,
            "today_ends" => LayDate::unix("tomorrow"),
            "week_ends" => LayDate::unix(self::$week_starts),
        ]);
    }

    public function update_reaction(PostReactionType $type) : bool
    {
        $this->update_prop("data_total", $this->data_total[$type->name] + 1);
        $this->update_prop("data_today", $this->data_today[$type->name] + 1);
        $this->update_prop("data_week", $this->data_week[$type->name] + 1);

        return $this->edit_self([
            "data_total" => json_encode($this->reaction_struct($this->data_total)),
            "data_today" => json_encode($this->reaction_struct($this->data_today)),
            "data_week" => json_encode($this->reaction_struct($this->data_week)),
            "today_ends" => LayDate::unix("tomorrow"),
            "week_ends" => LayDate::unix(self::$week_starts),
        ]);
    }
}