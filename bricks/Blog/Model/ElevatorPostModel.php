<?php
declare(strict_types=1);

namespace Elevator\Blog\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\ID\Gen;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $title
 * @property string $category
 * @property string $tag_id
 *
 * @property string $slug
 * @property bool $slug_editable
 *
 * @property string $cover_photo_slug
 * @property string $cover_photo
 * @property string|null $cover_photo_credit
 *
 * @property string $content_meta
 * @property string $content_body
 * @property int $duration
 * @property array $table_of_content
 * @property string|null $template
 *
 * @property PostStatus $post_status
 * @property int $schedule_time
 *
 * @property bool $author_is_ai
 * @property bool $notified_editors
 * @property bool $notified_authors
 * @property array $coauthors
 * @property string|null $revision_note
 *
 * @property string|null $calendar_id
 *
 * @property array|null $autosave_blob
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint properties

 * @property string category_id
 * @property string category_name
 * @property string category_slug
 * @property string category_desc
 *
 * @property string collection_id
 * @property string collection_name
 * @property string collection_slug
 * @property string collection_desc
 *
 * @property string tag_values
 *
 * @property string photo_url
 * @property array photo_metadata
 *
 * @property string author_id
 * @property string author_f_name
 * @property string author_l_name
 * @property string author_dp
 * @property array author_dp_metadata
 * @property string author_slug
 * @property string author_about
 *
 * @property string updater_id
 * @property string updater_f_name
 * @property string updater_l_name
 * @property string updater_dp
 * @property array updater_dp_metadata
 * @property string updater_slug
 * @property string updater_about
 */
class ElevatorPostModel extends BaseModelHelper
{
    public static string $table = "elevator_posts";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns): bool
    {
        $columns = $this->req_2_array($columns);

        return $this
            ->pre_run(fn(SQL $db) => $db->and_where(static::$table . ".client_id", $columns['client_id'] ?? $this->client_id()))
            ->get_by(static::$table . ".title", $columns['title'])->exists();
    }

    protected function cast_schema(): void
    {
        // Restore the autosaved values when model is called and user is logged in
        if(self::foundation()->user_session()::current_user() && $this->autosave_blob && !$this->restored_autosave) {
            // This is where the cast happens, so ignore the error here
            $data = array_merge($this->props(), json_decode($this->autosave_blob, true));
            $data['restored_autosave'] = true;
            $this->fill($data);
        }

        $this->cast(
            "post_status",
            PostStatus::class,
            parser: fn($v) => PostStatus::to_enum($v)
        );

        $this->cast("table_of_content", "array", []);
        $this->cast("tag_values", "array", []);
        $this->cast("coauthors", "array", []);
        $this->cast("author_dp_metadata", "array", []);
        $this->cast("updater_dp_metadata", "array", []);
        $this->cast("photo_metadata", "array", []);
        $this->cast("slug_editable", "bool", false);
        $this->cast("notified_editors", "bool", false);
        $this->cast("notified_authors", "bool", false);
        $this->cast("author_is_ai", "bool", false);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->post_category()->model(), "category")
            ->use("name", "category_name")
            ->use("slug", "category_slug")
            ->use("description", "category_desc")
            ->use("id", "category_id");

        $table = $this->join_table;
        $this->join(self::foundation()->post_collection()->model(), "collection")->to($table)
            ->use("id", "collection_id")
            ->use("slug", "collection_slug")
            ->use("description", "collection_desc")
            ->use("name", "collection_name");

        $this->join(self::foundation()->post_tag()->model(), "tag_id")
            ->use("values", "tag_values");

        $this->join(self::foundation()->file_store()->model(), "cover_photo")
            ->use("url", "photo_url")
            ->use("metadata", "photo_metadata");

        $this->join(self::foundation()->user_profile()->model(), "created_by")
            ->use("id", "author_id")
            ->use("first_name", "author_f_name")
            ->use("last_name", "author_l_name")
            ->use("dp", "author_dp_id")
            ->use("slug", "author_slug")
            ->use("about", "author_about");

        $table = $this->join_table;
        $this->join(self::foundation()->file_store()->model(), "dp")->to($table)
            ->use("url", "author_dp")
            ->use("metadata", "author_dp_metadata");

        $this->join(self::foundation()->user_profile()->model(), "updated_by")
            ->use("id", "updater_id")
            ->use("first_name", "updater_f_name")
            ->use("last_name", "updater_l_name")
            ->use("dp", "updater_dp")
            ->use("slug", "updater_slug")
            ->use("about", "updater_about");

        $table = $this->join_table;
        $this->join(self::foundation()->file_store()->model(), "dp")->to($table)
            ->use("url", "updater_dp")
            ->use("metadata", "updater_dp_metadata");

    }

    public function generate_slug(string $title) : string
    {
        return Gen::new()
            ->db_confirm(static::$table, "slug")
            ->slug($title);
    }

    public function autosave(RequestHelper $request) : self
    {
        $save = $request->props();

        $data = [];

        // Match values that are uuids. If so, they should be updated immediately, not saved to the blob
        // We don't want to mess up the join process of the model, when restoring the autosaved values
        foreach ($save as $k => $s) {
            if(preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $s . '')) {
                $data[$k] = $s;
                unset($save[$k]);
                continue;
            }
        }

        $data["autosave_blob"] = json_encode($save);

        $this->edit_self($data);

        return $this;
    }

    /**
     * @param string $post_id
     * @return array{
     *     prev: array{ title: string, slug: string },
     *     next: array{ title: string, slug: string },
     * }
     */
    public function post_prev_next(string $post_id) : array
    {
        $post = static::$table;
        $published = PostStatus::PUBLISH->name;

        $prev = self::db()->column("title, slug")
            ->where("deleted","0")
            ->and_where("post_status",$published)
            ->and_where("updated_at", "<", "(SELECT updated_at FROM $post WHERE id = '$post_id')")
            ->sort("updated_at","DESC")
            ->limit(1)
            ->then_select();

        $next = self::db()->column("title,slug")
            ->where("deleted","0")
            ->and_where("post_status", $published)
            ->and_where("updated_at", ">", "(SELECT updated_at FROM $post WHERE id = '$post_id')")
            ->sort("updated_at")
            ->limit(1)
            ->then_select();

        return [
            "prev" => $prev,
            "next" => $next,
        ];
    }

    public function by_slug(string $slug) : static
    {
        return $this->get_by(static::$table . ".slug", $slug);
    }

    public function all_by_status(PostStatus $status, int $page = 1, int $limit = 100) : array
    {
        return $this->pre_run(
            fn(SQL $db) => $db
                ->sort(static::$table . ".updated_at", "desc")
                ->sort(static::$table . ".created_at", "desc")
                ->limit($limit, $page)
        )->all_by_col("post_status", $status->name);
    }

    public function all_by_status_multi(PostStatus ...$status) : array
    {
        $status_all = $status;
        $status = $status_all[0];
        unset($status_all[0]);

        return $this->pre_run(function(SQL $db) use ($status_all) {
            foreach ($status_all as $status) {
                $db->or_where("post_status", $status->name);
            }
            $db->limit(100);
        })->all_by_col("post_status", $status->name);
    }

    public function all_by_search_query(string $query, bool $published_posts = false, int $page = 1, int $limit = 100) : array
    {
        $db = static::db();
        $post_table = static::$table;
        $published = PostStatus::PUBLISH->name;

        $relevance = $db->relevance_query ("$query", [
            "$post_table.title" => [
                'full' => 10,
                'keyword' => 8
            ],
            "$post_table.content_meta" => [
                'full' => 8,
                'keyword' => 5
            ],
            "ct2.values" => [
                'full' => 3,
                'is_json' => true
            ],

        ]);

        $db->column($this->fillable($db) . ", $relevance")
            ->where("$post_table.deleted", '0');

        $db->limit($limit, $page);

        if($published_posts)
            $db->and_where("$post_table.post_status", $published);

        $db->sort("relevance", "desc");

        $this->as_model($db);

        return $db->loop()->then_select();
    }

    public function related_articles(string $post_id, int $limit = 4) : array
    {
        $table = static::$table;
        $published = PostStatus::PUBLISH->name;

        $db = self::db();

        $this->as_model($db);

        return $db->column($this->fillable($db))
            ->where("$table.deleted", '0')
            ->and_where("$table.category", "(SELECT category FROM $table WHERE id='$post_id')")
            ->and_where("$table.id", "!=", $post_id)
            ->and_where("$table.post_status", $published)

            ->limit($limit)->loop()->then_select();
    }

    public function all_by_collection(string $collection, int $page = 1, int $limit = 100) : array
    {
        $db = self::db();

        $this->as_model($db);

        return $db->column($this->fillable($db))
            ->where(static::$table . ".deleted", '0')
            ->and_where("post_status", PostStatus::PUBLISH->name)
            ->wrap(
                "AND",
                fn(SQL $db) => $db->where("collection", $collection)
                    ->or_where("ct1.slug", $collection),
            )
            ->limit($limit, $page)->loop()->then_select();
    }

    public function all_by_category(string $category, int $page = 1, int $limit = 100) : array
    {
        $db = self::db();

        $this->as_model($db);

        return $db->column($this->fillable($db))
            ->where(static::$table . ".deleted", '0')
            ->and_where("post_status", PostStatus::PUBLISH->name)
            ->wrap(
                "AND",
                fn(SQL $db) => $db->where("category", $category)
                    ->or_where("ct0.slug", $category),
            )
            ->limit($limit, $page)->loop()->then_select();
    }

    public function all_by_author(string $author, int $page = 1, int $limit = 100) : array
    {
        $db = self::db();

        $this->as_model($db);

        return $db->column($this->fillable($db))
            ->where(static::$table . ".deleted", '0')
            ->and_where("post_status", PostStatus::PUBLISH->name)
            ->wrap(
                "AND",
                fn(SQL $db) => $db->where(static::$table . ".created_by", $author)
                    ->or_where("ct4.slug", $author),
            )
            ->limit($limit, $page)->loop()->then_select();
    }



}