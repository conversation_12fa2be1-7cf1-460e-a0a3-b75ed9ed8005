<?php
declare(strict_types=1);

namespace Elevator\Blog\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON>Layer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $name
 * @property string $slug
 * @property string $description
 * @property string $collection
 * @property bool $disabled
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint properties
 * @property string collection_name
 */
class ElevatorPostCategory extends BaseModelHelper
{
    public static string $table = "elevator_post_categories";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this
                ->pre_run(fn(SQL $db) => $db->and_where("client_id", $columns['client_id'] ?? $this->client_id()))
                ->count("name", $columns['name']) > 0;
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['id'],
            update_columns: ['deleted'],
            action: 'UPDATE'
        );
    }

    protected function cast_schema(): void
    {
        $this->cast("disabled", "bool", false);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->post_collection()->model(), "collection")
            ->use("name", "collection_name");
    }

    public function all_enabled(int $page = 1, int $limit = 100) : array
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->limit($limit, $page))
            ->all_by_col(static::$table . ".disabled", '0');
    }

    public function all_as_option(array $agr) : array
    {
        return $this->each(fn(self $data) => [
            "id" => $data->id,
            "name" => $data->name,
        ])->get_by_agr($agr, static::$table . ".id");
    }
}