<?php
declare(strict_types=1);

namespace Elevator\Blog\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use Brick<PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Blog\Enums\PostConversionGoal;
use Elevator\Blog\Enums\PostSocialChannels;
use Elevator\Blog\Enums\PostKpis;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Enums\PostTypes;
use Elevator\Blog\Enums\PostSearchIntent;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property null|string $post_id
 * @property string $post_title
 * @property array<string> $post_categories
 * @property string $post_summary
 * @property string $audience
 * @property array<PostConversionGoal> $goals
 * @property array<PostSearchIntent> $search_intents
 * @property string $pry_keyword
 * @property array $sec_keywords
 * @property PostTypes $post_type
 * @property null|string $author
 * @property PostStatus $status
 * @property string $publish_date // We don't want to store a unix date, we want a date like 2025-06-06 14:30; hence string
 * @property int $word_count
 * @property string $cta
 * @property array<string> $internal_links
 * @property array<string> $external_links
 * @property array<string> $assets_needed
 * @property array<PostSocialChannels> $social_channels
 * @property array<PostKpis> $kpis
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint Tables
 * @property string|null author_f_name
 * @property string|null author_l_name
 * @property string|null post_slug
 */
class ElevatorPostCalendar extends BaseModelHelper
{
    public static string $table = "elevator_post_calendars";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("post_categories", "array", []);
        $this->cast("goals", "array", []);
        $this->cast("search_intents", "array", []);
        $this->cast("sec_keywords", "array", []);
        $this->cast("post_type", PostTypes::class, default_value: null, parser: fn($v) => PostTypes::to_enum($v));
        $this->cast("status", PostStatus::class, default_value: null, parser: fn($v) => PostStatus::to_enum($v));
        $this->cast("word_count", "int", 0);
        $this->cast("internal_links", "array", []);
        $this->cast("external_links", "array", []);
        $this->cast("assets_needed", "array", []);
        $this->cast("social_channels", "array", []);
        $this->cast("kpis", "array", []);
    }

    protected function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['post_title'],
            action: 'REPLACE'
        );
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->user_profile()->model(), "author",)
            ->use("first_name", "author_f_name")
            ->use("last_name", "author_l_name");

        $this->join(self::foundation()->post_ctrl()->model(), "post_id",)
            ->use("slug", "post_slug");

    }

    /**
     * @param array{
     *     start_date?: string,
     *     end_date?: string,
     *     page?: int,
     *     limit?: int,
     * } $opts
     * @return array
     */
    public function list(array $opts) : array
    {
        $page = $opts['page'] ?? 1;
        $limit = $opts['limit'] ?? 500;

        return $this->pre_run(function (SQL $db) use ($opts) {
            $month_start = $opts['start_date'] ?? LayDate::date(format: "Y-m-01");
            $month_end = $opts['end_date'] ?? LayDate::date(format: "Y-m-t");

            $month_start = LayDate::unix($month_start);
            $month_end = LayDate::unix($month_end);

            $db->sort("publish_date", "desc")
                ->between("publish_date", "$month_start", "$month_end", true, false);

        })->all($page, $limit);
    }

    public function not_started_today(string $publish_date) : array
    {
        return $this->pre_run(function (SQL $db) use ($publish_date) {
            $db->and_where("publish_date", $publish_date);
        })->all_by_col("status", PostStatus::NOT_STARTED->name);
    }

}