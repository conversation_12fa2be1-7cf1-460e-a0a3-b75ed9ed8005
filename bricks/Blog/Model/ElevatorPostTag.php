<?php
declare(strict_types=1);

namespace Elevator\Blog\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property array $values
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorPostTag extends BaseModelHelper
{
    public static string $table = "elevator_post_tags";

    use CreatedBy;
    use HasFoundation;

    public function new_tag(string $tags) : static
    {
        return $this->add([
            "values" => json_encode(explode(",", $tags))
        ]);
    }

    public function edit_tag(string $tags) : bool
    {
        return $this->edit_self([
            "values" => json_encode(explode(",", $tags))
        ]);
    }

    protected function cast_schema() : void
    {
        $this->cast("values", "array");
    }
}