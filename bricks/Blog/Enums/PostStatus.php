<?php

namespace Elevator\Blog\Enums;

use Brick<PERSON><PERSON>er\Lay\Libs\Primitives\Enums\EnumHelper;

enum PostStatus {
    case DRAFT;
    case PUBLISH;
    case SCHEDULE;

    case NOT_STARTED; // Used by blog calendar only

    case PUB__REQUEST_APPROVAL;
    case PUB__REVIEWING;
    case PUB__REVISE_POST;

    use EnumHelper;

    public function stringify() : string
    {
        return str_replace(["PUB__", "_"], ["", " "], $this->name);
    }


    /**
     * @return array<int, array{
     *     id: string,
     *     name: string,
     * }>
     */
    public static function render(bool $show_not_started = false) : array
    {
        $all = [];

        foreach (self::cases() as $v) {
            if($v == self::PUB__REQUEST_APPROVAL || (!$show_not_started && $v == self::NOT_STARTED))
                continue;

            $all[] = [
                'id' => $v->name,
                'name' => str_replace(["PUB__", "_"], ["", " "], $v->name),
            ];
        }

        return $all;
    }
}