<?php

namespace Elevator\Blog\Traits;

use Brick<PERSON><PERSON>er\Lay\Libs\String\Escape;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Model\ElevatorPostModel;
use Elevator\Blog\Request\PostFileUploadRequest;
use Elevator\Blog\Request\PostRequestCreate;
use Elevator\Blog\Request\PostRequestEdit;
use Elevator\Utils\Enums\LogActivityTypes;

trait CreateEditDelete
{
    private function notify_editor(ElevatorPostModel $post) : void
    {
        if(
            $this->notify_author($post) ||
            $post->post_status != PostStatus::PUB__REQUEST_APPROVAL ||
            $post->notified_editors
        ) return;

        $author = !$post->author_is_ai ?
            $this->post_author($post->created_by) :
            static::foundation()->ai_agent()->as_author($post->created_by);

        $editors = [];

        foreach ( $this->post_editors() as $e ) {
            $editors[] = [
                "name" => $e['name'],
                "email" => $e['email'],
            ];
        }

        if(
            static::foundation()->mailer()->notify_editor([
                "editors" => $editors,
                "author" => $author,
                "link" => $this->update_post_link($post->id),
                "blog" => [
                    "title" => $post->title,
                    "id" => $post->id,
                ],
            ])
        )
            $post->edit_self([
                "notified_editors" => "1",
                "notified_authors" => "0",
                "post_status" => PostStatus::PUB__REVIEWING->name,
            ]);
    }

    private function notify_author(ElevatorPostModel $post) : bool
    {
        if($post->post_status != PostStatus::PUB__REVISE_POST)
            return false; // Not the right status;

        if($post->notified_authors)
            return true; // Author notified already;

        if($post->author_is_ai) {
            $post->edit_self([
                "notified_editors" => "0",
                "notified_authors" => "1",
            ]);

            return true;
        }

        $author = $this->post_author($post->created_by);

        if(
            static::foundation()->mailer()->notify_author([
                "email" => $author['email'],
                "name" => $author['name'],
                "note" => Escape::dirty($post->revision_note),
                "link" => $this->update_post_link($post->id),
                "blog" => [
                    "title" => $post->title,
                    "id" => $post->id,
                ],
            ])
        )
            $post->edit_self([
                "notified_editors" => "0",
                "notified_authors" => "1",
            ]);

        return true; // Done;
    }

    private function update_content_calendar(ElevatorPostModel $post) : void
    {
        $calendar = $this->calendar_model()->fill($post->calendar_id);

        if($calendar->is_empty()) return;

        $calendar->edit_self([
            "post_id" => $post->id,
            "post_title" => $post->title,
            "author" => $post->created_by,
            "status" => $post->post_status->name,
        ]);
    }

    private function update_sitemap_n_rss(ElevatorPostModel $post, bool $editing) : void
    {
        if($post->post_status != PostStatus::PUBLISH)
            return;

        $this->sitemap_ctrl()->update_index(
            $post->schedule_time ?? $post->updated_at ?? $post->created_at,
            $post->slug,
            !$editing
        );

        $this->rss_ctrl()->update_channel_and_item(
            $post->category,
            $post->id,
            $post->schedule ?? $post->updated_at ?? $post->created_at
        );

        $post->edit_self([ "slug_editable" => '0' ]);
    }

    private function add_tags(PostRequestCreate $request) : ?array
    {
        if(!isset($request->tags))
            return null;

        $tag = $this->tag_model()->new_tag($request->tags);

        if ($tag->is_empty())
            return self::res_warning("Could not create tags for post [$request->title] at the moment, please try again later");

        if (!$request->tag_id)
            $request->new_key("tag_id", $tag->id);

        $request->except("tags");

        return null;
    }

    private function auto_bind_calendar(PostRequestCreate $request) : void
    {
        if($request->calendar_id) return;

        $calendar = self::foundation()->post_calendar()->model()->get_by("post_title", $request->title);

        if ($calendar->exists()) $request->new_key("calendar_id", $calendar->id);
    }

    public function add(): array
    {
        $request = new PostRequestCreate();

        if($request->error)
            return self::res_warning($request->error);

        $post = $this->model();

        if($post->is_duplicate($request))
            return self::res_warning("Post exists already, please change the title or modify the existing post");

        return $post::orm()::scoped_transaction(function () use ($post, $request) {

            if($request->tags) {
                $tag = $this->add_tags($request);

                if($tag)
                    return $tag;
            }

            if($request->post_status == PostStatus::PUBLISH)
                $request->new_key("slug_editable", "0");

            // Clear autosave blob since the user is saving by themselves at this point
            $request->new_key("autosave_blob", null);

            $this->auto_bind_calendar($request);

            $post->add($request);

            if ($post->is_empty())
                return self::res_warning("Could not create post [$request->title] at the moment, please try again later");

            $this->notify_editor($post);

            $this->update_sitemap_n_rss($post, false);

            $this->update_content_calendar($post);

            $this->activity_log(
                LogActivityTypes::CREATE,
                "Post [$post->id] with title [$post->title] was created successfully",
            );

            return self::res_success("Changes saved successfully. Post status: [{$post->post_status->name}]", [
                "postId" => $post->id,
                "canEditSlug" => $post->slug_editable,
                "status" => $post->post_status->name
            ]);
        })['data'];
    }

    public function auto_save(): array
    {
        PostRequestEdit::$auto_save = true;

        $request = new PostRequestEdit();

        if($request->error)
            return self::res_warning($request->error);

        $post = $this->model();

        if(!$request->id && $post->is_duplicate($request))
            return self::res_warning("Post title exists already!");

        return $post::orm()::scoped_transaction(function () use ($post, $request) {

            if($request->tags) {
                $tag = $this->add_tags($request);

                if($tag)
                    return $tag;
            }

            $autosave = true;
            if($request->id) {
                $post->fill($request->id);
                $post->autosave($request);
            } else {
                $post->add($request);
                $autosave = false;
            }

            if ($post->is_empty())
                return self::res_warning("Failed to save your changes at the moment. Please take note");

            if(!$autosave)
                $this->activity_log(
                    LogActivityTypes::CREATE,
                    "Post [$post->id] with title [$post->title] was created successfully",
                );

            return self::res_success("Changes saved successfully!", [
                "postId" => $post->id,
            ]);
        })['data'];
    }

    public function edit(): array
    {
        $request = new PostRequestEdit();

        if($request->error)
            return self::res_warning($request->error);

        return $this->model()::orm()::scoped_transaction(function () use ($request) {
            $tag = $this->tag_model()->fill($request->tag_id);

            if($tag->is_empty()) {
                $tag = $this->add_tags($request);

                if($tag)
                    return $tag;
            }
            else {
                if(!$tag->edit_tag($request->tags))
                    return self::res_warning("Could not update your tags at the moment, please try again later");

                $request->except("tags");
            }

            // Post has already been checked if it's valid inside the request class
            $post = $this->model()->fill($request->id);

            if($request->post_status == PostStatus::PUBLISH)
                $request->new_key("slug_editable", "0");

            // Clear autosave blob since the user is saving by themselves at this point
            $request->new_key("autosave_blob", null);

            $this->auto_bind_calendar($request);

            if (!$post->edit_self($request))
                return self::res_warning("Could not save post at the moment, please try again later");

            if ($post::orm()->query_info['has_data']) {
                $post->refresh();

                $this->update_content_calendar($post);

                $this->activity_log(
                    LogActivityTypes::UPDATE,
                    "Blog post with ID [$post->id] was updated"
                );

                $this->notify_editor($post);

                $this->update_sitemap_n_rss($post, true);
            }

            return self::res_success( "Changes saved successfully. Post status: [{$post->post_status->name}]", [
                "postId" => $post->id,
                "canEditSlug" => $post->slug_editable
            ]);
        })['data'];
    }

    public function delete(): array
    {
        $id = self::clean(self::request()->id);

        $post = $this->model()->fill($id);

        if($post->is_empty() || !$post->delete_self())
            return self::res_success("Post does not exist, it may have been deleted already, please reload this page");

        $this->sitemap_ctrl()->remove_entry($post->slug);

        $this->rss_ctrl()->remove_entry($post->id);

        $this->activity_log(
            LogActivityTypes::DELETE,
            "Post [$post->id] with title [$post->title] was deleted",
        );

        return self::res_success( "Blog may have been deleted already!");
    }

    public function upload_file_on_text_editor() : array
    {
        $request = new PostFileUploadRequest();

        if($request->error)
            return self::res_warning($request->error);

        return self::res_success("Uploaded successfully", [
            "url" => self::foundation()->file_store()->model()->fill($request->text_editor_img)->url
        ]);
    }
}