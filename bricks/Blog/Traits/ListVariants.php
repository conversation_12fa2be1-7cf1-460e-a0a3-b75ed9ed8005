<?php

namespace Elevator\Blog\Traits;

use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Resource\PostResource;

trait ListVariants
{
    public function list_by_status(PostStatus $status, int $page, int $limit, bool $as_array = true) : array
    {
        return PostResource::collect(
            $this->model()->all_by_status($status, $page, $limit),
            as_array: $as_array
        );
    }

    public function published(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return $this->list_by_status(PostStatus::PUBLISH, $page, $limit, $as_array);
    }

    public function drafts(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return $this->list_by_status(PostStatus::DRAFT, $page, $limit, $as_array);
    }

    public function scheduled(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return $this->list_by_status(PostStatus::SCHEDULE, $page, $limit, $as_array);
    }

    public function approval_requested(): array
    {
        return PostResource::collect(
            $this->model()->all_by_status_multi(PostStatus::PUB__REQUEST_APPROVAL, PostStatus::PUB__REVIEWING)
        );
    }

    public function revision(int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        return $this->list_by_status(PostStatus::PUB__REVISE_POST, $page, $limit, $as_array);
    }

    public function search(string $query, int $page = 1, int $limit = 100, bool $as_array = true): array
    {
        self::cleanse($query);

        return PostResource::collect(
            $this->model()->all_by_search_query($query, false, $page, $limit),
            as_array: $as_array
        );
    }

    public function by_slug(string $slug) : ?PostResource
    {
        $post = $this->model()->by_slug($slug);

        if($post->is_empty())
            return null;

        return new PostResource($post);
    }

    public function by_id(string $id) : array
    {
        return $this->by_id_server($id)->props();
    }

    public function by_id_server(string $id) : PostResource
    {
        return new PostResource($this->model()->fill($id));
    }
}