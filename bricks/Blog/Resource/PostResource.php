<?php

namespace Elevator\Blog\Resource;

use <PERSON><PERSON><PERSON>er\Lay\Core\View\ViewSrc;
use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\LayFn;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Blog\Model\ElevatorPostModel;

/**
 * @property string postId
 * @property string title
 * @property string slug
 * @property bool canEditSlug
 * @property string duration
 * @property array{
 *     thumbnail: string|null,
 *     hd: string|null,
 *     ratio: array{ width: int, height: int},
 *     credit: string|null,
 * } photo
 * @property string metaContent
 * @property string postContent
 * @property array{
 *    type: string,
 *    level: int,
 *    text: string,
 *    slug: string,
 * } toc
 * @property string calendarId
 * @property array{
 *     id: string|null,
 *     name: string|null,
 *     slug: string|null,
 * } collection
 * @property array{
 *     id: string|null,
 *     name: string|null,
 *     slug: string|null,
 * } category
 * @property int dateUpdatedUnix
 * @property string dateUpdated
 * @property string dateCreated
 * @property array coauthors
 * @property string postRevisionNote
 * @property string postSchedule
 * @property array{
 *     id: string,
 *     name: string,
 * } status
 * @property array{
 *     id: string,
 *     values: array,
 * } tag
 * @property array{
 *     id: string,
 *     name: string,
 *     dp: string,
 *     slug: string,
 *     about: string,
 * } author
 * @property array{
 *     id: string,
 *     name: string,
 *     dp: string,
 *     slug: string,
 *     about: string,
 * } updater
 */
class PostResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorPostModel $data
         */

        if($data->cover_photo_slug)
            $data->update_prop("photo_url", $data->cover_photo_slug, false);

        return [
            "postId" => $data->id,
            "title" => $data->title,
            "slug" => $data->slug,
            "canEditSlug" => $data->slug_editable,
            "duration" => $data->duration ? str_pad($data->duration, 2, "0", STR_PAD_LEFT) : 0,
            "photo" => [
                "thumbnail" => $data->photo_url ? ViewSrc::gen($data->photo_url) : null,
                "hd" => $data->photo_url ? ViewSrc::gen($data->photo_url) : null,
                "ratio" => $data->photo_metadata['dimension'] ?? null,
                "credit" => $data->cover_photo_credit,
            ],
            "toc" => $data->table_of_content,
            "metaContent" => $data->content_meta ?? '',
            "postContent" => $data->content_body,
            "calendarId" => $data->calendar_id,
            "collection" => [
                "id" => $data->collection_id,
                "name" => $data->collection_name,
                "slug" => $data->collection_slug,
                "desc" => $data->collection_desc,
            ],
            "category" => [
                "id" => $data->category_id,
                "name" => $data->category_name,
                "slug" => $data->category_slug,
                "desc" => $data->category_desc,
            ],
            "dateUpdatedUnix" => $data->updated_at,
            "dateUpdated" => LayDate::date($data->updated_at, format_index: 3),
            "dateCreated" => LayDate::date($data->created_at, format_index: 3),
            "coauthors" => $data->coauthors,
            "postRevisionNote" => $data->revision_note,
            "postSchedule" => $data->schedule_time,
            "status" => [
                "id" => $data->post_status->name,
                "name" => $data->post_status->stringify(),
            ],
            "tag" => [
                "id" => $data->tag_id,
                "values" => $data->tag_values,
            ],
            "author" => [
                "id" => $data->author_id,
                "name" => $data->author_f_name . ' ' . $data->author_l_name,
                "dp" => $data->author_dp,
                "slug" => $data->author_slug,
                "about" => $data->author_about,
            ],
            "updater" => [
                "id" => $data->updater_id,
                "name" => $data->updater_f_name . ' ' . $data->updater_l_name,
                "dp" => $data->updater_dp,
                "slug" => $data->updater_slug,
                "about" => $data->updater_about,
            ],
        ];
    }
}