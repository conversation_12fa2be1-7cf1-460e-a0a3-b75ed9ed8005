<?php

namespace Elevator\Blog\Resource;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Blog\Model\ElevatorPostCategory;

/**
 * @property string id
 * @property string name
 * @property string slug
 * @property string desc
 * @property string collection
 * @property string collectionId
 * @property string dateCreated
 * @property string|null dateUpdated
 */
class PostCategoryResource extends ResourceHelper
{
    protected function schema(object $data): array
    {

        /**
         * @var ElevatorPostCategory $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "slug" => $data->slug,
            "desc" => $data->description,
            "collection" => $data->collection_name,
            "collectionId" => $data->collection,
            "dateUpdated" => $data->updated_at ? LayDate::date($data->updated_at, format_index: 2) : null,
            "dateCreated" => LayDate::date($data->created_at, format_index: 2),
        ];
    }
}