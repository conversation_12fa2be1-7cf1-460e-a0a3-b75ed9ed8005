<?php

namespace Elevator\Blog\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Blog\Model\ElevatorPostCollection;

/**
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property string $desc
 * @property string $date
 */
class PostCollectionResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorPostCollection $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "slug" => $data->slug,
            "desc" => $data->description,
            "date" => $data->updated_at ?? $data->created_at,
        ];
    }
}