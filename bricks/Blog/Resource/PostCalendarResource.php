<?php

namespace Elevator\Blog\Resource;

use Brick<PERSON>ayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Blog\Model\ElevatorPostCalendar;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string calendarId
 * @property string publishDate
 * @property string title
 * @property array categories
 * @property string summary
 * @property string audience
 * @property array goals
 * @property array intents
 * @property string pryKeyword
 * @property array secKeywords
 * @property string postTypeId
 * @property string postType
 * @property string postStatusId
 * @property string postStatus
 * @property int wordCount
 * @property string cta
 * @property array internalLinks
 * @property array externalLinks
 * @property array assetsNeeded
 * @property array socialChannels
 * @property array kpis
 * @property string|null author
 * @property string|null authorId
 * @property array|null publishPost
 * @property string dateCreated
 * @property string dateUpdated
 */
class PostCalendarResource extends ResourceHelper
{
    use HasFoundation;

    protected function schema(object $data): array
    {
        /**
         * @var ElevatorPostCalendar $data
         */

        $category = self::foundation()->post_category()->model()->all_as_option($data->post_categories);

        $rtn = [
            "calendarId" => $data->id,
            "title" => $data->post_title,
            "categories" => $category,
            "summary" => $data->post_summary,
            "audience" => $data->audience,
            "goals" => $data->goals,
            "intents" => $data->search_intents,
            "pryKeyword" => $data->pry_keyword,
            "secKeywords" => $data->sec_keywords,
            "postType" => $data->post_type->stringify(),
            "postTypeId" => $data->post_type->name,
            "postStatus" => $data->status->stringify(),
            "postStatusId" => $data->status->name,
            "publishDate" => $data->publish_date,
            "wordCount" => $data->word_count,
            "cta" => $data->cta,
            "internalLinks" => $data->internal_links,
            "externalLinks" => $data->external_links,
            "assetsNeeded" => $data->assets_needed,
            "socialChannels" => $data->social_channels,
            "kpis" => $data->kpis,
            "dateCreated" => LayDate::date($data->created_at, format_index: 3),
            "dateUpdated" => LayDate::date($data->updated_at, format_index: 3),
        ];

        if($data->author) {
            $rtn['author'] = $data->author_f_name . " " . $data->author_l_name;
            $rtn['authorId'] = $data->author;
        }

        if($data->post_id) {
            $rtn['publishPost'] = [
                "id" => $data->post_id,
                "slug" => $data->post_slug,
            ];
        }

        return $rtn;
    }
}