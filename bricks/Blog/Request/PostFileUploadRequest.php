<?php

namespace Elevator\Blog\Request;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string text_editor_img
 */
class PostFileUploadRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules() : void
    {
        $file_type = static::foundation()->file_store_type()->type_by_slug("blog-thumbnail");

        $this->vcm([
            'field' => 'text_editor_img', 'new_file_name' => 'blog_asset',
            "is_file" => true,
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "allowed_types" => $file_type->allowed_types,
            "sub_dir" => static::foundation()->post_ctrl()->store() . "general",
            "bucket_url" => static::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => static::foundation()->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => static::foundation()->file_store()
                ->new_file($response, [
                    "name" => "Image from Text Editor",
                    "provider" => FileModelProviderDefault::BLOG_POST,
                    "is_public" => true
                ])
        ]);

    }
}