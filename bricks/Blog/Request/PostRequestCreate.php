<?php

namespace Elevator\Blog\Request;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use <PERSON>Layer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Utils\MarkdownAnalyzer;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string id
 * @property string title
 * @property string created_by
 * @property string category
 * @property string tags
 * @property string slug
 * @property string cover_photo
 * @property string|null cover_photo_ratio
 * @property string|null cover_photo_credit
 * @property string|null content_meta
 * @property string content_body
 * @property string template
 * @property string post_status
 * @property int|null schedule_time
 * @property string|null coauthors
 * @property string table_content
 * @property string calendar_id
 * @property int duration
 */
class PostRequestCreate extends RequestHelper
{
    use HasFoundation;

    protected function pre_validate(): void
    {
        self::vcm_start(self::request(), [
            'required' => false,
        ]);
    }

    public function general_rule() : void
    {
        $this->vcm([ 'field' => 'title', 'required' => true ]);

        // Only used by Workflow to mark that it created by an AI
        $this->vcm([ 'field' => 'author_is_ai', 'default_value' => false ]);

        $this->vcm([
            'field' => 'author',
            'alias' => 'created_by',
            'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => $this->get('author_is_ai') ?
                    static::foundation()->ai_agent()->model()->fill($v)->exists() :
                    static::foundation()->user_profile()->model()->fill($v)->exists(),
                "message" => $this->get('author_is_ai') ?
                    "Author is not valid. Current author might be a user not an AI as indicated" :
                    "Author is not valid. Current author might be an AI, and if that's the case, you need to assign this post to a human author"
            ],
        ]);

        $this->vcm([
            'field' => 'category',
            "must_validate" => [
                "fun" => fn ($v) => static::foundation()->post_category()->model()->fill($v)->exists(),
                "message" => "An invalid post category was received"
            ],
            "required" => true,
        ]);
        $this->vcm([ 'field' => 'tags' ]);
        $this->vcm([ 'field' => 'slug', 'max_length' => 100, "clean" => ["escape" => EscapeType::P_URL] ]);

        $file_type = static::foundation()->file_store_type()->type_by_slug("blog-thumbnail");

        $this->vcm([
            'field' => 'cover_photo',
            'new_file_name' => 'thumbnail',
            "max_size" => $file_type->max_size,
            "dimension" => $file_type->max_dimension,
            "allowed_types" => $file_type->allowed_types,
            'maybe_file' => true,
            "sub_dir" => static::foundation()->post_ctrl()->store() . $this->get('id'),
            "bucket_url" => static::foundation()->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => static::foundation()->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => static::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('title') . " Cover Photo",
                    "provider" => FileModelProviderDefault::BLOG_POST,
                    "is_public" => true
                ])
        ]);
        $this->vcm([ 'field' => 'cover_photo_credit', 'max_length' => 200 ]);


        $this->vcm([ 'field' => 'meta_content', 'alias' => 'content_meta', 'max_length' => 300 ]);
        $this->vcm([ 'field' => 'body_content', 'alias' => 'content_body', 'required' => true,  ]);
        $this->vcm([ 'field' => 'post_template', 'alias' => 'template' ]);


        $this->vcm([
            'field' => 'post_status',
            "required" => true,
            "must_validate" => [
                "fun_str" => function($v) : ?string {
                    if(!PostStatus::is_enum($v))
                        return "An invalid post status was submitted";

                    return null;
                },
            ],
            "return_schema" => function ($v) {
                $post = static::foundation()->post_ctrl();

                if( $post->user_can_publish_post() && $v == PostStatus::PUB__REQUEST_APPROVAL)
                    return PostStatus::PUBLISH->name;

                if(
                    !$post->user_can_publish_post()
                    && in_array($v, [ PostStatus::PUBLISH->name, PostStatus::SCHEDULE->name ])
                ) return PostStatus::PUB__REQUEST_APPROVAL->name;

                if(
                    !$post->user_can_publish_post()
                    && $v == PostStatus::PUB__REVISE_POST->name
                ) return PostStatus::PUB__REVIEWING->name;

                return $v;
            },
        ]);

        $post_status = $this->get('post_status');

        if($post_status == PostStatus::PUB__REVISE_POST->name)
            $this->vcm([ 'field' => 'revision_note', 'required' => true, "after_clean" => fn($v) => nl2br($v) ]);

        if($post_status == PostStatus::SCHEDULE)
            $this->vcm([ 'field' => 'schedule_time', 'return_schema' => fn($v) => LayDate::in_seconds($v), 'required' => true ]);

        $this->vcm([
            'field' => 'coauthors[]',
            "must_validate" => [
                'fun_str' => function ($v) {
                    $author = $this->get('new_author');
                    $co_author = self::foundation()->user_profile()->model()->fill($v);

                    if($co_author->is_empty())
                        return "An invalid co-author was included in the list of collaborators";

                    if($author == $co_author->id)
                        return "You cannot include the author of this post as a collaborator since they are authors already";

                    return null;
                }
            ]
        ]);
        $this->vcm([ 'field' => 'calendar_id', ]);
    }

    public function analyse_post(array $data) : array
    {
        $md = (new MarkdownAnalyzer)->analyze($this->content_body);

        $data['table_of_content'] = json_encode($md['toc']);
        $data['duration'] = $md['duration'];

        return $data;
    }

    protected function rules() : void
    {
        self::vcm_new_entry("id", SQL::new()->uuid());

        $this->general_rule();
    }

    protected function update_cover_photo(array &$data) : void
    {
        if($this->cover_photo && str_starts_with($this->cover_photo, 'http')) {
            $data["cover_photo_slug"] = $this->cover_photo;
            unset($data["cover_photo"]);
        }
    }

    protected function post_validate(array $data): array
    {
        $this->update('slug', self::foundation()->post_ctrl()->model()->generate_slug($this->slug ?? $this->title));

        $this->update_cover_photo($data);

        return $this->analyse_post($data);
    }
}