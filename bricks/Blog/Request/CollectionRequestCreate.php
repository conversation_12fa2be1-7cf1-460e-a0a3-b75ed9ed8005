<?php

namespace Elevator\Blog\Request;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use BrickLayer\Lay\Libs\String\Escape;

/**
 * @property string $name
 * @property string $description
 */
class CollectionRequestCreate extends RequestH<PERSON>per
{
    public function general_rule() : void
    {
        $this->vcm([ 'field' => 'name' ]);
        $this->vcm([ 'field' => 'description', 'required' => false, 'max_length' => 200, 'after_clean' => fn($v) => nl2br($v) ]);
    }

    protected function rules() : void
    {
        $this->general_rule();
    }

    protected function post_validate(array $data): array
    {
        $data['slug'] = Escape::clean($data['name'], EscapeType::P_URL);
        return $data;
    }
}