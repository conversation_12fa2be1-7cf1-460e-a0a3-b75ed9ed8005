<?php

namespace Elevator\Blog\Request;


/**
 * @property string tag_id
 */
class PostRequestEdit extends PostRequestCreate
{
    public static bool $auto_save = false;

    protected function rules() : void
    {
        $this->vcm([
            'field' => 'id',
            'required' => !self::$auto_save,
            "is_uuid" => true,
            "must_validate" => [
                "fun" => fn($v) => static::foundation()->post_ctrl()->model()->fill($v)->exists(),
                "message" => "You are trying to update an invalid post. ID is invalid"
            ]
        ]);

        $this->vcm([ 'field' => 'tag_id' ]);

        $this->general_rule();

    }

    protected function post_validate(array $data): array
    {
        $post = static::foundation()->post_ctrl()->model()->fill($this->id);

        $current_user = $post->created_by();

        $data['updated_by'] = $current_user != $post->created_by ? $current_user : $post->updated_by;

        if($post->slug_editable && $this->slug) {
            $this->update('slug', $post->generate_slug($this->slug));
        }

        $this->update_cover_photo($data);

        if(self::$auto_save) return $data;

        return $this->analyse_post($data);
    }
}