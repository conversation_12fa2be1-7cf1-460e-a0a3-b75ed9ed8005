<?php

namespace Elevator\Blog\Request;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use BrickLayer\Lay\Libs\String\Escape;
use Elevator\Blog\Enums\PostConversionGoal;
use Elevator\Blog\Enums\PostKpis;
use Elevator\Blog\Enums\PostSearchIntent;
use Elevator\Blog\Enums\PostSocialChannels;
use Elevator\Blog\Enums\PostStatus;
use Elevator\Blog\Enums\PostTypes;
use Elevator\Utils\ElevatorFoundation;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property array $assets_needed
 * @property string|null $author
 * @property string|null $audience
 * @property string $cta
 * @property array<string> $goals
 * @property string $kpis // Array of kpis
 * @property string $post_categories // Array of post categories
 * @property string $post_summary
 * @property string $post_title
 * @property string $post_type
 * @property string $pry_keyword
 * @property null|string $sec_keywords // Array of secondary keywords
 * @property string $search_intents // Array of search intents for the post
 * @property string $publish_date
 * @property string $social_channels // Array of post channels
 * @property int $word_count
 * @property null|array<string> $internal_links
 * @property null|array<string> $external_links
 */
class CalendarRequestCreate extends RequestHelper
{
    use HasFoundation;
    
    protected function pre_validate(): void
    {
        self::vcm_start(self::request(), [
            'required' => true,
            'json_encode' => false,
            'each' => fn ($v, $index) => Escape::clean($v, EscapeType::STRIP_TRIM_ESCAPE),
            "group_result" => true
        ]);
    }

    private function my_validator(string $field, mixed &$post, bool $is_required, callable $checker, array $options) : array
    {
        $has_error = false;

        if($is_required && empty($post)) {
            $this->report_error($field, $options['field_name'] . " is required!");
            $has_error = true;
        }

        if(!$has_error) {
            $vals = explode(",", $post);

            foreach ($vals as $val) {
                if (!$checker($val)) {
                    $this->report_error($field, "An invalid {$options['field_name']} was received!");
                    $has_error = true;
                    break;
                }
            }

            $post = json_encode($vals);
        }

        return [
            "apply_clean" => false,
            "add_to_entry" => !$has_error,
        ];
    }

    protected function rules() : void
    {
        $clean_multi_str = function ($val) {
            $posts = explode("\n", $val);

            foreach ($posts as $i => $p) {
                $posts[$i] = Escape::clean($p, EscapeType::STRIP_TRIM_ESCAPE);
            }

            return json_encode($posts);
        };

        $this->vcm([ 'field' => 'assets_needed[]', 'each' => $clean_multi_str, ]);

        $this->vcm([
            'field' => 'author[]',
            'must_validate' => fn($v) => self::foundation()->user_profile()->model()->fill($v)->exists(),
            "required" => false
        ]);

        $this->vcm([
            'field' => 'post_id[]',
            'must_validate' => fn($v) => self::foundation()->post_ctrl()->model()->fill($v)->exists(),
            "required" => false
        ]);

        $this->vcm([ 'field' => 'audience[]', "required" => false ]);
        $this->vcm([ 'field' => 'cta[]', ]);

        $this->vcm([
            'field' => 'goals[][]',
            'validator' => function ($field, &$posts, $is_required, $option) {
                return $this->my_validator($field, $posts, $is_required, fn($v) => PostConversionGoal::is_enum($v), $option);
            },
        ]);

        $this->vcm([
            'field' => 'kpis[][]',
            'validator' => function ($field, &$posts, $is_required, $option) {
                return $this->my_validator($field, $posts, $is_required, fn($v) => PostKpis::is_enum($v), $option);
            },
        ]);

        $this->vcm([
            'field' => 'post_categories[][]',
            'validator' => function ($field, &$posts, $is_required, $option) {
                return $this->my_validator(
                    $field,
                    $posts,
                    $is_required,
                    fn($v) => self::foundation()->post_category()->model()->fill($v)->exists(),
                    $option
                );
            },
        ]);
        $this->vcm([ 'field' => 'post_summary[]', 'max_length' => 300, 'each' => fn($v) => nl2br(Escape::clean($v)) ]);
        $this->vcm([ 'field' => 'post_title[]' ]);
        $this->vcm([ 'field' => 'post_type[]', 'must_validate' => fn($v) => PostTypes::is_enum($v) ]);
        $this->vcm([ 'field' => 'status[]', 'must_validate' => fn($v) => PostStatus::is_enum($v) ]);
        $this->vcm([ 'field' => 'pry_keyword[]' ]);
        $this->vcm([ 'field' => 'sec_keywords[]', 'each' => $clean_multi_str, "required" => false, ]);
        $this->vcm([
            'field' => 'search_intents[][]',
            'validator' => function ($field, &$posts, $is_required, $option) {
                return $this->my_validator($field, $posts, $is_required, fn($v) => PostSearchIntent::is_enum($v), $option);
            },
        ]);
        $this->vcm([ 'field' => 'publish_date[]', 'is_date' => true ]);
        $this->vcm([
            'field' => 'social_channels[][]',
            'validator' => function ($field, &$posts, $is_required, $option) {
                return $this->my_validator($field, $posts, $is_required, fn($v) => PostSocialChannels::is_enum($v), $option);
            },
        ]);
        $this->vcm([ 'field' => 'word_count[]', 'is_num' => true ]);
        $this->vcm([ 'field' => 'internal_links[]', 'each' => $clean_multi_str, "required" => false ]);
        $this->vcm([ 'field' => 'external_links[]', 'each' => $clean_multi_str, "required" => false ]);

    }

}