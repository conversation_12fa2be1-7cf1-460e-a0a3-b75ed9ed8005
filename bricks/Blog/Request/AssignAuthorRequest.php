<?php

namespace Elevator\Blog\Request;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $author
 * @property string $calendar_id
 * @property string $notify_author
 */
class AssignAuthorRequest extends RequestHelper
{
    use HasFoundation;

    protected function rules() : void
    {
        $this->vcm([
            'field' => 'author', 'is_uuid' => true,
            "must_validate" => [
                "fun" => fn($v) => self::foundation()->user_profile()->model()->fill($v)->exists(),
                "message" => "Author is invalid"
            ]
        ]);

        $this->vcm([
            'field' => 'calendar_id', 'is_uuid' => true,
            "must_validate" => [
                "fun" => fn($v) => self::foundation()->post_calendar()->model()->fill($v)->exists(),
                "message" => "Calendar entry does not exist"
            ]
        ]);

        $this->vcm([ 'field' => 'notify_author', 'is_bool' => true, "required" => false, ]);

    }

}