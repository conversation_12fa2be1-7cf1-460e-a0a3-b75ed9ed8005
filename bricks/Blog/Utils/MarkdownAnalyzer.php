<?php

namespace Elevator\Blog\Utils;

use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\CommonMark\Node\Block\FencedCode;
use League\CommonMark\Extension\CommonMark\Node\Block\Heading;
use League\CommonMark\Extension\CommonMark\Node\Block\IndentedCode;
use League\CommonMark\Node\Inline\Text;
use League\CommonMark\Node\Node;
use League\CommonMark\Parser\MarkdownParser;

class MarkdownAnalyzer
{
    protected MarkdownParser $parser;

    protected array $toc = [];
    protected int $wordCount = 0;

    public function __construct()
    {
        $environment = new Environment();
        $environment->addExtension(new CommonMarkCoreExtension());
        $this->parser = new MarkdownParser($environment);
    }

    /**
     * @param string $markdown
     * @return array{
     *     toc: array{
     *        type: string,
     *        level: int,
     *        text: string,
     *        slug: string,
     *     },
     *     word_count: int,
     *     duration: int,
     * }
     */
    public function analyze(string $markdown): array
    {
        $document = $this->parser->parse($markdown);
        $this->walk($document);

        return [
            'toc' => $this->toc,
            'word_count' => $this->wordCount,
            'duration' => $this->estimateReadingTime($this->wordCount),
        ];
    }

    protected function walk(Node $node): void
    {
        foreach ($node->children() as $child) {
            if ($node instanceof FencedCode || $node instanceof IndentedCode) {
                continue;
            }

            if ($child instanceof Heading) {
                $text = $this->extractText($child);

                $this->toc[] = [
                    'type' => "h",
                    'level' => $child->getLevel(),
                    'text' => $text,
                    'slug' => $this->slugify($text),
                ];
            }

            // Accumulate word count from inline text anywhere else
            $this->wordCount += str_word_count($this->extractText($child));

            if ($child->hasChildren()) {
                $this->walk($child);
            }
        }
    }

    protected function extractText(Node $node): string
    {
        $text = '';

        foreach ($node->children() as $child) {
            if ($child instanceof Text) {
                $text .= $child->getLiteral() . ' ';
            } elseif ($child->hasChildren()) {
                $text .= $this->extractText($child);
            }
        }

        return trim($text);
    }

    public static function slugify(string $text): string
    {
        return strtolower(trim(preg_replace('/[^a-z0-9]+/i', '-', $text), '-'));
    }

    protected function estimateReadingTime(int $wordCount, int $wpm = 200): int
    {
        return max(1, (int) ceil($wordCount / $wpm));
    }
}
