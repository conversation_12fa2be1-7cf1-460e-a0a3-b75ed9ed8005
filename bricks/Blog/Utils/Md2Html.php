<?php

namespace Elevator\Blog\Utils;

use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\CommonMark\Node\Block\Heading;
use League\CommonMark\MarkdownConverter;
use League\CommonMark\Node\Node;
use League\CommonMark\Renderer\ChildNodeRendererInterface;
use League\CommonMark\Renderer\NodeRendererInterface;
use League\CommonMark\Util\HtmlElement;

class Md2Html
{
    protected MarkdownConverter $converter;

    public function __construct()
    {
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
            'max_nesting_level' => 5
        ]);

        $environment->addExtension(new CommonMarkCoreExtension());

        $this->renderers($environment);

        $this->converter = new MarkdownConverter($environment);
    }

    public function render(string $markdown): string
    {
        return $this->converter->convert(str_replace(["&gt;"], [">"], $markdown));
    }

    protected function renderers(Environment $environment) : void
    {
        $environment->addRenderer(Heading::class, new class implements NodeRendererInterface {
            public function render(Node $node, ChildNodeRendererInterface $childRenderer): HtmlElement|string|null
            {
                if (!($node instanceof Heading)) {
                    throw new \InvalidArgumentException('Expected Heading node');
                }

                $content = $childRenderer->renderNodes($node->children());
                $level = $node->getLevel();
                $tag = "h$level";

                return new HtmlElement($tag, ['class' => "article-heading-$level", 'id' => MarkdownAnalyzer::slugify($content)], $content);
            }
        });
    }
}