<?php

namespace Elevator\SysArch\Controller;

use Brick<PERSON><PERSON>er\Lay\Core\Api\Enums\ApiStatus;
use <PERSON><PERSON>ayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Libs\Dir\LayDir;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorSystemArch extends ElevatorController
{
    use HasFoundation;

    public static function not_sys_admin() : ?array
    {
        if (static::foundation()->user_session()::get('is_super_admin'))
            return null;

        return self::res_warning(message: "Unauthorized user access!", code: ApiStatus::UNAUTHORIZED);
    }

    public static function get_super_admin() : array
    {
        return static::foundation()->user_profile()->get_super_admins();
    }

    public function delete_exceptions() : array
    {
        if($no = self::not_sys_admin())
            return $no;

        $dir = LayConfig::server_data()->exceptions;

        if( LayDir::is_empty($dir))
            return self::res_warning("Exception directory is probably empty");

        LayDir::read($dir, function ($file, $dir) {
            unlink($dir . $file);
        }, false);

        return self::res_success("Exceptions have been deleted successfully!");
    }

    public function delete_webhook_file() : array
    {
        if($no = self::not_sys_admin())
            return $no;

        $file = LayConfig::server_data()->temp . "git_webhook.txt";

        if(!file_exists($file))
            return self::res_warning( "There is no webhook file to delete!");

        unlink($file);
        return self::res_success( "File been deleted successfully!");
    }

    public function delete_composer_log_file() : array
    {
        if($no = self::not_sys_admin())
            return $no;

        $file = LayConfig::server_data()->temp . "deploy_composer_output.txt";

        if(!file_exists($file))
            return self::res_warning( "There is no composer output file to delete!");

        unlink($file);
        return self::res_success( "File been deleted successfully!");
    }

    public function delete_fetched_email_logs() : array
    {
        if($no = self::not_sys_admin())
            return $no;

        $dir = LayConfig::server_data()->temp . "emails" . DIRECTORY_SEPARATOR;

        if(LayDir::is_empty($dir))
            return self::res_warning("Email directory is probably empty");

        LayDir::read($dir, function ($file, $dir) {
            unlink($dir . $file);
        }, false);

        return self::res_success( "Email logs cleared successfully");
    }

    //TODO: Implement this
    // Hard Delete soft deleted content from all tables in the project after a specified date: 30 days
    public function delete_stale_data() : void
    {

    }

    public function model(): BaseModelHelper {}
}