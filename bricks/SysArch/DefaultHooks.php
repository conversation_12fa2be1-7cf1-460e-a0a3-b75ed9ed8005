<?php

namespace Elevator\SysArch;

use Brick<PERSON>ayer\Lay\Libs\Cron\CronController;
use <PERSON>Layer\Lay\Libs\Cron\LayCron;
use Elevator\SysArch\Controller\ElevatorSystemArch;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Traits\HasFoundation;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorSystemArch $arch;
    
    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix . "/sys-arch");
    }

    protected function pre_hook() : void
    {
        self::$arch = self::foundation()->sys_arch();
    }

    protected function hooks(): void
    {
        $this->primary_prefix();

        $this->elevator_middleware()->is_sys_arch();

        $this->post("exceptions/delete")->limit(1, "1 hour")->bind(fn() => static::$arch->delete_exceptions());
        $this->post("webhook/delete")->limit(1, "1 hour")->bind(fn() => static::$arch->delete_webhook_file());
        $this->post("composer/delete")->limit(1, "1 hour")->bind(fn() => static::$arch->delete_composer_log_file());
        $this->post("email-log/delete")->limit(1, "1 hour")->bind(fn() => static::$arch->delete_fetched_email_logs());

        CronController::created_by(self::foundation()->user_session()::current_user());

        $this->post("cron-jobs/new")->bind(fn() => CronController::new()->add());
        $this->get("cron-jobs/list")->bind(fn() => CronController::new()->list());
        $this->post("cron-jobs/run")->bind(fn() => CronController::new()->run_script());
        $this->post("cron-jobs/pause")->bind(fn() => CronController::new()->pause_script());
        $this->post("cron-jobs/play")->bind(fn() => CronController::new()->play_script());
        $this->post("cron-jobs/delete-log")->limit(1,"1 hour")->bind(function() {
            LayCron::new()->clear_log();

            return ['code' => 200, "message" => "Log file cleared successfully"];
        });

    }
}