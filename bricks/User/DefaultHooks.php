<?php

namespace Elevator\User;

use Elevator\User\Controller\ElevatorActivityLogController;
use Elevator\User\Controller\ElevatorAuthController;
use Elevator\User\Controller\ElevatorPositionController;
use Elevator\User\Controller\ElevatorProfileController;
use Elevator\User\Controller\ElevatorRoleController;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Enums\PermissionAccessType;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorAuthController $auth;
    protected static ElevatorProfileController $profile;
    protected static ElevatorActivityLogController $activity;
    protected static ElevatorRoleController $role;
    protected static ElevatorPositionController $position;

    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix);
    }

    protected function pre_hook() : void
    {
        $foundation = self::foundation();

        self::$auth = $foundation->user_auth();
        self::$profile = $foundation->user_profile();
        self::$activity = $foundation->user_activity();
        self::$role = $foundation->user_role();
        self::$position = $foundation->user_position();
    }

    protected function login_group() : void
    {
        $this->group("auth", function () {
            $this->post("reg_sa")->bind(fn() => static::$auth->register_super_admin());
            $this->post("login")->bind(
                fn() => static::foundation()->use_php_session() ? static::$auth->login_with_session() : static::$auth->login_with_jwt()
            );
            $this->post("password/request")
                ->bind(fn() => static::$auth->reset_password_outside_session());
            $this->post("password/new/{token}")
                ->bind(fn(string $token) => static::$auth->set_new_password_outside_session($token));
        });
    }

    protected function hooks(): void
    {
        $this->primary_prefix();

        $this->login_group();

        $this->elevator_middleware()->is_auth();

        $this->group("user", function () {
            $this->post("change-password")->bind(fn() => static::$auth->change_password());
            $this->post("update-profile")->bind(fn() => static::$profile->update_me());
        });

        $this->group("system", function () {
            $this->get("users-plain")->bind(fn() => static::$profile->normal_users());

            $this->get("all-users/{page}")
                ->bind(fn($page) => static::$profile->list((int) $page));

            $this->get("all-but-me/")
                ->middleware($this->elevator_middleware()->is_permitted(static::$profile))
                ->bind(fn() => static::$profile->all_but_me());

            $this->post("users/update-data/{id}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$profile))
                ->bind(fn($id) => static::$profile->update_via_post($id));

            $this->post("users/new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$auth, PermissionAccessType::CREATE))
                ->bind(fn() => static::$auth->register_normal_admin());

            $this->post("users/resend-reg")
                ->middleware($this->elevator_middleware()->is_permitted(static::$auth, PermissionAccessType::CREATE))
                ->bind(fn() => static::$auth->resend_register_link());

            $this->post("users/delete")
                ->middleware($this->elevator_middleware()->is_permitted(static::$auth, PermissionAccessType::DELETE))
                ->bind(fn() => static::$auth->delete());

//            $this->post("users/search", ApiReturnType::HTML)->bind(fn() => SystemUsers::new()->search_for_users());

            // default
            $this->get("logs")
                ->name("current-month-logs")
                ->middleware($this->elevator_middleware()->is_permitted(static::$activity))
                ->bind(fn() => static::$activity->list());

            $this->post("logs")
                ->name("specific-range-logs")
                ->middleware($this->elevator_middleware()->is_permitted(static::$activity))
                ->bind(fn() => static::$activity->list());


            $this->get("roles/list")
                ->middleware($this->elevator_middleware()->is_permitted(static::$role))
                ->bind(fn() => static::$role->non_super_roles());

            $this->post("roles/new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$role, PermissionAccessType::CREATE))
                ->bind(fn() => static::$role->add());

            $this->post("roles/edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$role, PermissionAccessType::EDIT))
                ->bind(fn() => static::$role->edit());

            $this->get("roles/modules")
                ->middleware($this->elevator_middleware()->is_permitted(static::$role))
                ->bind(fn() => static::$role->list_modules());


            $this->get("user-position/list")
                ->middleware($this->elevator_middleware()->is_permitted(static::$position))
                ->bind(fn() => static::$position->list());

            $this->post("user-position/new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$position, PermissionAccessType::CREATE))
                ->bind(fn() => static::$position->add());

            $this->post("user-position/edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$position, PermissionAccessType::EDIT))
                ->bind(fn() => static::$position->edit());
        });
    }

}