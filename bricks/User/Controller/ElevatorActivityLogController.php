<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use Elevator\User\Model\ElevatorActivityLogModel;
use Elevator\User\Resources\UserActivityResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorActivityLogController extends ElevatorController implements HasPermission
{
    use HasFoundation;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::SystemLog;
    }

    /**
     * @abstract  Child class must override this method to return a model that extends `ElevatorAuthModel`
     */
    public function model(): ElevatorActivityLogModel
    {
        return new ElevatorActivityLogModel();
    }

    public function list(int $page = 1, int $limit = 100) : array
    {
        $range = self::request(false)->range ?? null;

        $start_date = null;
        $end_date = null;

        if($range) {
            $range = explode(" to ", $range);

            $start_date = $range[0];
            $end_date = $range[1];
        }

        return UserActivityResource::collect(
            $this->model()->list([
                "page" => $page,
                "length" => $limit,
                "start_date" => $start_date,
                "end_date" => $end_date,
            ])
        );
    }
}
