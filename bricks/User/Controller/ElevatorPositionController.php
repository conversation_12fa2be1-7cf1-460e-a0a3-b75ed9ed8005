<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use Elevator\User\Model\ElevatorPositionModel;
use Elevator\User\Requests\UserPositionCreateRequest;
use Elevator\User\Requests\UserPositionEditRequest;
use Elevator\User\Resources\UserPositionResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorPositionController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::UserPosition;
    }

    public function model() : ElevatorPositionModel
    {
        return new ElevatorPositionModel();
    }

    public function add() : array
    {
        $request = new UserPositionCreateRequest();

        if($request->error)
            return self::res_warning($request->error);

        $role = $this->model();

        if($role->is_duplicate($request))
            return self::res_warning("Position exists already!");

        $role->add($request);

        if($role->is_empty())
            return self::res_warning("Position was not added, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "A new position with ID [$role->id] was created; name [$role->name]"
        );

        return self::res_success("Position [$role->name] was created successfully");
    }

    public function edit() : array
    {
        $request = new UserPositionEditRequest();

        if($request->error)
            return self::res_warning($request->error);

        $role = $this->model()->fill($request->id);

        if($role->is_empty())
            return self::res_warning("Position does not exist!");

        $role->edit_self($request);

        if($role->is_empty())
            return self::res_warning("Position was not edited, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Position with ID [$role->id] was edited; name [$role->name]"
        );

        return self::res_success("Position [$role->name] was updated successfully");
    }

    public function list() : array
    {
        return UserPositionResource::collect(
            $this->model()->all()
        );
    }
}
