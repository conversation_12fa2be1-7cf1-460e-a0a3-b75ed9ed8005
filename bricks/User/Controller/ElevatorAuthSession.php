<?php

namespace Elevator\User\Controller;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Core\Api\Enums\ApiStatus;
use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use BrickLayer\Lay\Core\LayException;
use <PERSON><PERSON>ayer\Lay\Libs\LayCookieStorage;
use <PERSON>Layer\Lay\Libs\LayCrypt\Enums\JwtError;
use Brick<PERSON><PERSON>er\Lay\Libs\LayCrypt\LayCrypt;
use BrickL<PERSON>er\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\LayPassword;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use Elevator\User\Model\ElevatorAuthModel;
use Elevator\User\Model\ElevatorProfileModel;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorAuthSession
{
    use HasFoundation;
    use ControllerHelper;

    public static function new() : static
    {
        return new static();
    }

    protected static string $session_key;

    protected static string $session_sub_key = "ELEV_USR";

    protected static function session_key(): string
    {
        if(isset(static::$session_key))
            return static::$session_key;

        return static::$session_key = "ELEV_SESS_" . LayConfig::app_id();
    }

    public static function current_user() : ?string
    {
        return static::get("profile_id", false);
    }

    public static function current_client() : ?string
    {
        return static::get("client_id", false);
    }

    public static function update_session(string $key, mixed $value) : void
    {
        $_SESSION[static::session_key()][static::$session_sub_key][$key] = $value;
    }

    public static function unset_session(string $key) : void
    {
        unset($_SESSION[static::session_key()][static::$session_sub_key][$key]);
    }

    /**
     * This is just a sample of how you could implement your own
     *
     * @abstract You may overwrite if needed
     */
    public static function start_jwt(ElevatorProfileModel $profile, array $others = []): array
    {
        $others['profile_id'] = $profile->id;
        $others['client_id'] = $profile->client_id;

        $token_expiry = LayDate::unix('1 hour');
        $refresh_token_expiry = LayDate::unix('4 hour');

        $token = LayCrypt::gen_jwt(
            payload: $others,
            expires: $token_expiry,
        );

        return [
            "token" => $token,
            "expiry" => $token_expiry,
            "refreshToken" => static::foundation()->user_refresh_token()->refresh_token($profile->id, $refresh_token_expiry),
            "refreshTokenExpiry" => $refresh_token_expiry,
        ];
    }

    /**
     * This is just a sample of how you could implement your own
     *
     * @abstract Must overwrite if you want to use it
     */
    public static function start_session(ElevatorProfileModel $profile, ElevatorAuthModel $auth, ?array $other = null): void
    {
        $role = static::foundation()->user_role()->model()->fill($profile->role);

        if($profile->dp)
            $profile->update_prop("dp", $profile->dp);

        static::update_session('profile_id', $profile->id);
        static::update_session('client_id', $profile->client_id);

        static::update_session('email', $auth->login_name);
        static::update_session('user', $profile->props());
        static::update_session('is_super_admin', $role->is_super);

        if($auth->reset_login_pass)
            static::update_session('must_reset_password', $auth->reset_login_pass);

        if(isset($_COOKIE['PHPSESSID']))
            static::update_session('csrf', LayCrypt::csrf_gen($_COOKIE['PHPSESSID']));

        static::update_session('permissions', [...$profile->x_permissions, ...$role->permissions]);

        $profile->edit_self([
            "refresh_session" => '0'
        ]);
    }

    /**
     * Returns the value contained in the session key
     *
     * @param string $key
     * @param bool $log_error when an undefined key is accessed [default: true]
     * @return mixed
     */
    public static function get(string $key, bool $log_error = true): mixed
    {
        $session = $_SESSION[static::session_key()][static::$session_sub_key] ?? null;

        if ($key === "*")
            return $session;

        if (!isset($session[$key])) {
            if($log_error)
                LayException::log("Key [$key] does not exist in session", log_title: "OutOfBoundKeyAccess");

            return null;
        }

        return $session[$key];
    }

    public static function validate_session(bool $validate_csrf = true): array
    {
        $current_user = static::current_user();
        $csrf = fn() => !$validate_csrf || self::validate_csrf();

        if(!$current_user) {
            if(self::check_cookie($validate_csrf))
                return static::res_success( "Session refreshed from cookie!");

            return static::res_error("Not in session", code: ApiStatus::SESSION_EXPIRED);
        }

        if(!$csrf())
            return static::res_error(
                "Mismatched session token! Please login again",
                code: ApiStatus::SESSION_EXPIRED,
            );

        return static::res_success( "Session valid!");
    }

    public static function validate_jwt(): array
    {
        $token = LayConfig::get_header('bearer');

        if(!$token)
            return static::res_error("Not in session", code: ApiStatus::SESSION_EXPIRED);

        // TODO: Implement remembering from cookies

        $jwt = LayCrypt::verify_jwt($token);

        if (!$jwt['valid']) {
            if($jwt['why'] == JwtError::EXPIRED && self::check_cookie())
            return static::res_error($jwt['message'] . " Please login again", code: ApiStatus::SESSION_EXPIRED);
        }

        $current_user = static::current_user();

        if($current_user != null && ($jwt['data']['profile_id'] != $current_user))
            return static::res_error("Mismatched session. Please login again", code: ApiStatus::SESSION_EXPIRED);

        foreach ($jwt['data'] as $key => $sess) {
            self::update_session($key, $sess);
        }

        return static::res_success( "Session valid!");
    }

    private static function validate_csrf(): bool
    {
        $token = LayConfig::get_header('X-CSRF-TOKEN');

        if (empty($token)) return false;

        if(isset($_COOKIE['PHPSESSID']) && hash_equals($token, LayCrypt::csrf_gen($_COOKIE['PHPSESSID'])))
            return true;

        return false;
    }

    public static function clear_jwt() : array
    {
        //TODO: Accept the JWT, go to the database to invalidate or delete the refresh token
        static::clear_session();

        return static::res_success( "Session cleared, logging you out");
    }

    public static function clear_session() : array
    {
        $session = @$_SESSION[static::session_key()][static::$session_sub_key];

        if ($session)
            unset($_SESSION[static::session_key()][static::$session_sub_key]);

        return static::res_success( "Session cleared, logging you out");
    }

    private static function check_cookie(bool $validate_csrf): bool
    {
        $test = LayCookieStorage::validate_cookie();

        if (!$test['found']) return false;

        $auth = self::foundation()->user_auth()->model()->fill($test['data']['created_by']);

        if($auth->is_empty()) return false;

        $profile = self::foundation()->user_profile()->model()->fill($auth->last_workspace);

        if($profile->is_empty())
            $profile->by_auth_id($auth->id);

        if($profile->is_empty()) return false;

        if($validate_csrf && !self::validate_csrf())

        self::start_session($profile, $auth);

        return true;
    }

}