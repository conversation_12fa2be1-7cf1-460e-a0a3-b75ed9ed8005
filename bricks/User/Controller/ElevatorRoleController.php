<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\User\Model\ElevatorRoleModel;
use Elevator\User\Requests\UserRoleCreateRequest;
use Elevator\User\Requests\UserRoleEditRequest;
use Elevator\User\Resources\UserRoleResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Enums\PermissionAccessType;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\PermissionManager;
use Elevator\Utils\Traits\ActivityLog;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorRoleController extends ElevatorController implements HasLog, HasPermission
{
    use ActivityLog;
    use HasFoundation;

    public function model(): ElevatorRoleModel
    {
        return new ElevatorRoleModel();
    }

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::UserRole;
    }

    public static function permitted(PermissionInterface|HasPermission $module, PermissionAccessType $access = PermissionAccessType::VIEW) : bool
    {
        if ($module instanceof HasPermission) $module = $module::module_id();

        return PermissionManager::check_access(
            $module, $access,
            static::foundation()->user_session()::get('permissions', false) ?? [],
            static::foundation()->user_session()::get('is_super_admin', false) ?? false
        )['grant'];
    }

    public function list_modules() : array
    {
        return PermissionManager::list_modules(self::foundation()->permission_class());
    }

    public function add() : array
    {
        $request = new UserRoleCreateRequest();

        if($request->error)
            return self::res_warning($request->error);

        $role = $this->model();

        if($role->is_duplicate($request))
            return self::res_warning("Role exists already!");

        $role->add($request);

        if($role->is_empty())
            return self::res_warning("Role was not added, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "A new role with ID [$role->id] was created; name [$role->name]"
        );

        return self::res_success("Role [$role->name] was created successfully");
    }

    public function edit() : array
    {
        $request = new UserRoleEditRequest();

        if($request->error)
            return self::res_warning($request->error);

        $role = $this->model()->fill($request->id);

        if($role->is_empty())
            return self::res_warning("Role does not exist!");

        $role->edit_self($request);

        if($role->is_empty())
            return self::res_warning("Role was not edited, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Role with ID [$role->id] was edited; name [$role->name]"
        );

        return self::res_success("Role [$role->name] was updated successfully");
    }

    public function non_super_roles() : array
    {
        return UserRoleResource::collect(
            $this->model()->pre_run(
                fn(SQL $db) => $db->and_where("is_super", "!=", "1")
            )->all()
        );
    }

}
