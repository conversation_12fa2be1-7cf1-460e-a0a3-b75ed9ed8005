<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use Elevator\User\Model\ElevatorProfileModel;
use Elevator\User\Requests\UpdateUserProfileRequest;
use Elevator\User\Resources\UserResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorProfileController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Users;
    }

    public function model() : ElevatorProfileModel
    {
        return new ElevatorProfileModel();
    }

    private static function auth_session() : ElevatorAuthSession
    {
        return static::foundation()->user_session();
    }

    public function list(int $page = 1) : array
    {
        return UserResource::collect(
            $this->model()->all($page, 120)
        );
    }

    public function normal_users(int $page = 1) : array
    {
        return UserResource::collect(
            $this->model()->list_users(false, $page)
        );
    }

    public function as_resource(array|object $data) : UserResource
    {
        return new UserResource($data);
    }

    public function get_super_admins() : array
    {
        return UserResource::collect(
            $this->model()->list_users(true)
        );
    }

    public function all_but_me(int $page = 1) : array
    {
        return UserResource::collect(
            $this->model()->all_but_me(static::auth_session()::current_user(), $page)
        );
    }

    public function update_me() : array
    {
        $user = $this->model()->fill_current_user();

        if($user->is_empty())
            return self::res_warning("Could not find user, you may need to login again");

        $request = new UpdateUserProfileRequest();

        if(!$user->edit_self($request))
            return self::res_warning("Could not edit user profile at the moment, please try again later");

        static::auth_session()::update_session("user", $user->refresh()->props());

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "User profile was edited, the following things were edited: [" . implode(",", $request->props()) . "]"
        );

        return self::res_success("Profile updated successfully");
    }

    public function update_via_post(string $profile_id) : array
    {
        $user = $this->model()->fill(self::clean($profile_id));

        if($user->is_empty())
            return self::res_warning("User is invalid, could not updated details at the moment");

        $request = new UpdateUserProfileRequest();

        $request->new_key("refresh_session", "1");

        if(!$user->edit_self($request))
            return self::res_warning("Could not edit user profile at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "User profile was edited from the users page, the following things were edited: [" . implode(",", $request->props()) . "]"
        );

        return self::res_success("Details updated successfully");
    }

    public function by_slug(string $slug) : ?UserResource
    {
        $model = $this->model()->get_by("slug", self::clean($slug));

        if($model->is_empty())
            return null;

        return new UserResource($model);
    }
}
