<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use Elevator\User\Model\ElevatorRefreshTokenModel;
use Elevator\Utils\ElevatorController;

class ElevatorRefreshTokenController extends ElevatorController
{
    public function model(): ElevatorRefreshTokenModel
    {
        return new ElevatorRefreshTokenModel();
    }

    public final function refresh_token(string $user_id, string|int $expiry): string
    {
        return $this->model()->new_token($user_id, $expiry)->token;
    }

}
