<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use BrickLayer\Lay\Orm\SQL;
use Elevator\User\Model\ElevatorAuthLogModel;
use Elevator\User\Resources\AuthAuthLogResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorAuthLogController extends ElevatorController
{
    use HasFoundation;

    public function model(): ElevatorAuthLogModel
    {
        return new ElevatorAuthLogModel();
    }

    public function list(string $user_id) : array
    {
        return AuthAuthLogResource::collect(
            $this->model()->list_log(self::clean($user_id))
        );
    }

    public function current_user_logs() : array
    {
        return $this->list(static::foundation()->user_session()::current_user());
    }

}
