<?php
declare(strict_types=1);

namespace Elevator\User\Controller;

use Elevator\AiAgent\Controller\ElevatorAgentController;
use Elevator\User\Model\ElevatorAuthModel;
use Elevator\User\Model\ElevatorProfileModel;
use Elevator\User\Traits\AiAuthTrait;
use Elevator\User\Traits\Login;
use Elevator\User\Traits\PasswordReset;
use Elevator\User\Traits\QuickVerticalAuth;
use Elevator\User\Traits\Register;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorAuthController extends ElevatorController implements HasLog, HasPermission
{
    use Login, PasswordReset, Register;

    use <PERSON>Vert<PERSON>Auth, AiAuthTrait;

    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Users;
    }

    public function model(): ElevatorAuthModel
    {
        return new ElevatorAuthModel();
    }

    private static function auth_session() : ElevatorAuthSession
    {
        return static::foundation()->user_session();
    }

    private function role_ctrl(): ElevatorRoleController
    {
        return static::foundation()->user_role();
    }

    private function ai_agent_ctrl() : ElevatorAgentController
    {
        return static::foundation()->ai_agent();
    }

    private function profile_model(): ElevatorProfileModel
    {
        return static::foundation()->user_profile()->model();
    }
}
