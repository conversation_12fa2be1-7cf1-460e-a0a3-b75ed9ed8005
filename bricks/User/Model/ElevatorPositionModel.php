<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $name
 * @property string|null $note
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint Properties
 * @property string first_name
 * @property string last_name
 */
class ElevatorPositionModel extends BaseModelHelper
{
    public static string $table = "elevator_user_positions";

    use CreatedBy;
    use HasFoundation;

    protected function prefill() : void
    {
        $this->join(self::foundation()->user_profile()->model(), "created_by")
            ->use("first_name")
            ->use("last_name");
    }

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this
                ->pre_run(
                    fn(SQL $db) => $db->and_where(static::$table . ".client_id", $columns['client_id'] ?? $this->client_id())
                )
                ->count(static::$table . ".name", $columns['name']) > 0;
    }

    public function by_name(string $name, ?string $client_id = null) : static
    {
        return $this
            ->pre_run(
                fn(SQL $db) => $db->and_where(static::$table . ".client_id", $client_id ?? $this->client_id())
            )
            ->get_by(static::$table . ".name", $name);
    }

    public function get_super_position(?string $client_id = null) : static
    {
        return $this->by_name(self::foundation()->super_user()['position'], $client_id);
    }

    public function new_super_position(string $created_by, ?string $client_id = null) : static
    {
        return $this->add([
            "name" => self::foundation()->super_user()['position'],
            "note" => "Added as a default value. Please edit this position description",
            "created_by" => $created_by,
            "client_id" => $client_id ?? $this->client_id()
        ]);
    }
}
