<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use Brick<PERSON>ayer\Lay\Libs\ID\Gen;
use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\FileStore\Model\ElevatorFileStoreModel;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $profile_id
 * @property string $auth_id
 * @property string $first_name
 * @property string $last_name
 * @property string $middle_name
 * @property bool $refresh_session
 * @property array $x_permissions
 * @property string $role
 * @property string $position
 * @property string $dp
 * @property string $about
 * @property string $slug
 * @property array $social
 * @property array $dataset
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint Columns
 * @property string dp_src
 * @property array dp_metadata
 * @property string email
 * @property string position_name
 * @property string role_name
 * @property bool reset_login_pass
 * @property bool is_super
 * @property array permissions
 */
class ElevatorProfileModel extends BaseModelHelper
{
    public static string $table = "elevator_user_profiles";
    protected static string $profile_id_prefix = "ELV-PR-";

    use CreatedBy;
    use HasFoundation;

    protected function gen_profile_id(string $client_id) : string
    {
        return Gen::new()
            ->db_confirm(
                static::$table,
                "profile_id",
                fn(SQL $db) => $db->and_where(static::$table . ".client_id", $client_id)
            )
            ->prepend(static::$profile_id_prefix)->gen();
    }

    protected function cast_schema() : void
    {
        $this->cast("is_super", "bool", false);
        $this->cast("x_permissions", "array", []);
        $this->cast("permissions", "array", []);
        $this->cast("social", "array", []);
        $this->cast("refresh_session", "bool", false);
        $this->cast("reset_login_pass", "bool", false);
        $this->cast("dataset", "array", []);
        $this->cast("dp_metadata", "array", []);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->file_store()->model(), "dp")
            ->use("url", "dp_src")
            ->use("metadata", "dp_metadata");

        $this->join(self::foundation()->user_auth()->model(), "auth_id")
            ->use("login_name", "email")
            ->use("reset_login_pass");

        $this->join(self::foundation()->user_role()->model(), "role")
            ->use("name", "role_name")
            ->use("permissions")
            ->use("is_super");

        $this->join(self::foundation()->user_position()->model(), "position")
            ->use("name", "position_name");
    }

    public function fill_current_user() : self
    {
        return $this->fill(static::foundation()->user_session()::current_user());
    }

    public function by_auth_id(string $auth_id, ?string $client_id = null) : static
    {
        $this->pre_run(
            fn(SQL $db) => $db->and_where(static::$table . ".client_id", $client_id ?? $this->client_id())
        );

        return $this->get_by("auth_id", $auth_id);
    }

    public function new_profile(array $column) : static
    {
        $client_id = $column['client_id'] ?? $this->client_id();

        $slug = Gen::new()
            ->db_confirm(
                static::$table,
                "slug",
                fn (SQL $db) => $db
                    ->and_where(static::$table . ".client_id", $client_id)
            )
            ->slug($column['last_name'] . ' ' . $column['first_name'] . ' ' . ($column['middle_name'] ?? ''));

        return $this->add(array_merge([
            "auth_id" => $column['auth_id'],
            "first_name" => $column['first_name'],
            "middle_name" => $column['middle_name'] ?? null,
            "last_name" => $column['last_name'],
            "slug" => $slug,
            "profile_id" => $this->gen_profile_id($client_id),
            "role" => $column['role'],
            "position" => $column['position'],
            "created_by" => $column['created_by'] ?? static::created_by(),
            "client_id" => $client_id,
        ], $column));
    }

    /**
     * Get all profiles associated with an auth id
     * @param string $auth_id
     * @return array
     */
    public function all_profiles(string $auth_id) : array
    {
        return $this
            ->all_by_col("auth_id", $auth_id);
    }


    public function all_but_me(string $id, int $page = 1, int $limit = 100) : array
    {
        return $this
            ->pre_run(fn(SQL $db) => $db
                ->limit($limit, $page)
                ->and_where("is_super", "!=", "1")
            )
            ->all_by_col(static::$table . ".id", "!=", $id);
    }

    public function concat_name() : string
    {
        return $this->name ?? ($this->first_name . " " . $this->last_name);
    }

    /**
     * @param array<int, string> $permission
     * @param string|null $client_id
     * @param bool $include_super_user
     * @param int $page
     * @param int $limit
     */
    public function users_by_permission(
        array $permission,
        ?string $client_id = null,
        bool $include_super_user = true,
        bool $include_current_user = false,
        int $page = 1, int $limit = 100,
    ): array
    {
        $table = static::$table;

        $db = self::db()->limit($limit, $page);

        $db->column($this->fillable($db));

        $db->where("$table.deleted", "0")
            ->and_where("$table.client_id", $client_id ?? $this->client_id());

        if($this->created_by() && !$include_current_user)
            $db->and_where("$table.auth_id", "!=", $this->created_by());

        $db->wrap(
            "and",
            function () use ($db, $permission, $include_super_user) {

                if($include_super_user)
                    $db->where("is_super", "1");

                foreach ($permission as $i => $pem) {
                    $db->json_contains("permissions", $pem, ($i == 0 && !$include_super_user) ? '' : "OR");
                    $db->json_contains("x_permissions", $pem, "OR");
                }

            }
        );

        $this->exec_pre_run($db);

        return $db->loop()->then_select();
    }

    public function list_users(bool $super_admins, int $page = 1, int $limit = 100, ?string $client_id = null): array
    {
        $table = static::$table;

        $db = self::db()->limit($limit, $page);
        $db->column($this->fillable($db));

        $db->where("$table.deleted", "0")
            ->and_where("$table.auth_id", "!=", $this->created_by())
            ->and_where("$table.client_id", $client_id ?? $this->client_id())
            ->and_where("is_super", $super_admins ? "=" : "!=", "1");

        $this->exec_pre_run($db);

        $this->as_model($db);

        return $db->loop()->then_select();
    }

}
