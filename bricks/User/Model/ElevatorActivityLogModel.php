<?php

namespace Elevator\User\Model;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use <PERSON><PERSON><PERSON>er\Lay\Core\View\Domain;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $message
 * @property array $dataset
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint properties
 * @property string user_id
 * @property string first_name
 * @property string last_name
 */
class ElevatorActivityLogModel extends BaseModelHelper
{
    public static string $table = "elevator_user_activity_logs";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema(): void
    {
        $this->cast("dataset", "array");
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->user_profile()->model(), "created_by")
            ->use("id", "user_id")
            ->use("first_name")
            ->use("last_name");
    }

    /**
     * @param string $message
     * @param string $created_by
     * @param string|null $client_id
     * @param array{
     *   module_enum: PermissionInterface,
     *   activity_enum: LogActivityTypes,
     * } $dataset
     * @return void
     */
    public function log(string $message, string $created_by, ?string $client_id, array $dataset = []): void
    {
        if(Domain::is_in_use()) {
            $route = Domain::current_route_data("*");
            $route = @$route['domain_uri'] . @$route['route'];
        }
        else
            $route = "CLI-MODE";

        $dataset = [
            "activity" => $dataset['activity_enum']->name,
            "route" => $route,
            "referer" => $_SERVER['HTTP_REFERER'] ?? 'unknown',
            "ip" => LayConfig::get_ip(),
            "device" => LayConfig::user_agent()
        ];

        $this->add([
            "client_id" => $client_id ?? $this->client_id(),
            "message" => $message,
            "dataset" => json_encode($dataset),
            "created_by" => $created_by,
        ]);
    }

    /**
     * @param array{
     *     client_id: string,
     *     start_date?: string,
     *     end_date?: string,
     *     page?: int,
     *     limit?: int,
     * } $opts
     * @return array
     */
    public function list(array $opts) : array
    {
        return $this->pre_run(function (SQL $db) use ($opts) {
            $month_start = $opts['start_date'] ?? LayDate::date(format: "Y-m-01");
            $month_end = $opts['end_date'] ?? LayDate::date(format: "Y-m-t");

            $month_start = LayDate::unix($month_start);
            $month_end = LayDate::unix($month_end);

            $limit = $opts['limit'] ?? 500;
            $page = $opts['page'] ?? 1;

            $db->limit($limit, $page)
                ->between(static::$table . ".created_at", "$month_start", "$month_end", false, false)
                ->sort(static::$table . ".created_at", "desc");
        })->all_by_col(static::$table . ".client_id", $opts['client_id'] ?? $this->client_id());
    }
}