<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use <PERSON><PERSON>ayer\Lay\Core\LayConfig;
use <PERSON>Layer\Lay\Libs\LayCrypt\LayCrypt;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string     user_id
 * @property string     $token
 * @property int        expires_at
 * @property array      device
 *
 * @property string     $id
 * @property bool       $deleted
 * @property string     $deleted_by
 * @property int        $deleted_at
 * @property string|null $created_by
 * @property int        $created_at
 * @property string|null $updated_by
 * @property int        $updated_at
 */
class ElevatorRefreshTokenModel extends BaseModelHelper
{
    public static string $table = "elevator_jwt_refresh_tokens";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("device", "array", []);
    }

    public function new_token(string $user_id, string|int $expiry): self
    {
        $expiry = LayDate::unix($expiry);

        $token = LayCrypt::gen_jwt(
            payload: ["elv_key" => self::uuid()],
            expires: $expiry,
        );

        $device = json_encode([
            "ip" => LayConfig::get_ip(),
            "env" => LayConfig::user_agent()
        ]);

        return $this->add([
            'user_id' => $user_id,
            'token' => $token,
            'expires_at' => $expiry,
            'device' => $device,
        ]);
    }

    public function by_user_id(string $user_id) : self
    {
        return $this->get_by("user_id", $user_id);
    }

    public function by_token(string $token) : self
    {
        return $this->get_by("token", $token);
    }



}
