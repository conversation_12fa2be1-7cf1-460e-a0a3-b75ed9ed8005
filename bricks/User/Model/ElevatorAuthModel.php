<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property bool       $is_bot
 * @property string     $last_workspace
 * @property bool       $show_all_workspaces
 * @property string     $login_name
 * @property string     $login_pass
 * @property string     $otp_token
 * @property int        $otp_token_expires
 * @property bool       $is_active
 * @property bool       $reset_login_pass
 * @property bool       $must_log_out
 *
 * @property string     $id
 * @property bool       $deleted
 * @property string     $deleted_by
 * @property int        $deleted_at
 * @property string|null $created_by
 * @property int        $created_at
 * @property string|null $updated_by
 * @property int        $updated_at
 */
class ElevatorAuthModel extends BaseModelHelper
{
    public static string $table = "elevator_user_auths";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("show_all_workspaces", "bool", false);
        $this->cast("is_bot", "bool", false);
        $this->cast("is_active", "bool", false);
        $this->cast("reset_login_pass", "bool", false);
        $this->cast("must_log_out", "bool", false);
    }

    public function by_login_name(string $login_name) : static
    {
        return $this->get_by("login_name", $login_name);
    }

    public function by_token(string $token) : static
    {
        return $this->get_by("otp_token", $token);
    }

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->count("login_name", $columns['login_name']) > 0;
    }

    public function ai_users() : array
    {
        return $this->pre_run(
            fn(SQL $db) => $db->fun(function ($res) {
                return [
                    "name" => $res['login_name'],
                    "authId" => $res['id'],
                ];
            })
        )->all_by_col("is_bot", "1");
    }

}
