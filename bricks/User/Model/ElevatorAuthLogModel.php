<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON>Layer\Lay\Orm\SQL;
use Elevator\User\Enums\AuthTypes;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property bool $cached_login
 * @property string $profile_id
 * @property AuthTypes $auth_type
 * @property array $env_info
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorAuthLogModel extends BaseModelHelper
{
    public static string $table = "elevator_user_auth_logs";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("auth_type", AuthTypes::class, parser: fn($v) => AuthTypes::to_enum($v));
        $this->cast("env_info", "array");
        $this->cast("cached_login", "bool");
    }

    public function log(
        string    $auth_id,
        string    $profile_id,
        AuthTypes $auth_type,
        bool      $login_saved = false,
        ?string   $client_id = null
    ) : static
    {
        $country = "Default - Nigeria";
        $ip = LayConfig::get_ip();
        $geo = LayConfig::geo_data();

        if(isset($geo->region))
            $country = $geo->region . ", " . $geo->city . ", " . $geo->country;

        $agent = LayConfig::user_agent();

        return $this->add([
            "cached_login" => $login_saved ? '1' : '0',
            "auth_type" => $auth_type->name,
            "profile_id" => $profile_id,
            "env_info" => json_encode([
                "forwarded" => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? "NON-FORWARDED",
                "ip" => $ip,
                "user_agent" => $agent,
                "os" => $agent['platform'] ?? 'NON-SPECIFIED',
                "country" => $country
            ]),
            "created_by" => $auth_id,
            "client_id" => $client_id ?? $this->client_id()
        ]);
    }

    public function list_log(string $user_id, int $limit = 10) : array
    {
        return $this->pre_run(fn(SQL $db) => $db->limit($limit))->all_by_col("profile_id", $user_id);
    }
}
