<?php
declare(strict_types=1);

namespace Elevator\User\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $name
 * @property string|null $note
 * @property array<string> $permissions
 * @property bool $is_super
 * @property string $scope
 * @property int $total_users
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint Properties
 * @property string first_name
 * @property string last_name
 */
class ElevatorRoleModel extends BaseModelHelper
{
    public static string $table = "elevator_user_roles";

    use CreatedBy;
    use HasFoundation;

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this
                ->pre_run(
                    fn(SQL $db) => $db->and_where(static::$table . ".client_id", $columns['client_id'] ?? $this->client_id())
                )
                ->count(static::$table . ".name", $columns['name']) > 0;
    }

    protected function cast_schema(): void
    {
        $this->cast("permissions", 'array', []);
        $this->cast("is_super", 'bool', false);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->user_profile()->model(), "created_by")
            ->use("first_name")
            ->use("last_name");
    }

    public function by_name(string $name, ?string $client_id = null) : static
    {
        return $this
            ->pre_run(
                fn(SQL $db) => $db->and_where(static::$table . ".client_id", $client_id ?? $this->client_id())
            )
            ->get_by("name", $name);
    }

    public function get_super_role(?string $client_id = null) : static
    {
        return $this->by_name(self::foundation()->super_user()['role'], $client_id);
    }

    public function new_super_role(string $created_by, ?string $client_id = null) : static
    {
        return $this->add([
            "name" => self::foundation()->super_user()['role'],
            "note" => "Added as a default value. Please edit this role description",
            "is_super" => 1,
            "created_by" => $created_by,
            "client_id" => $client_id ?? $this->client_id()
        ]);
    }
}
