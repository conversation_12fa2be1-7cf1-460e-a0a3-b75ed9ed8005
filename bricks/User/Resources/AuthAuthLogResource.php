<?php

namespace Elevator\User\Resources;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\User\Model\ElevatorAuthLogModel;

/**
 * @property array $envInfo
 * @property bool $savedLogin
 * @property string $authType
 * @property string $date
 */
class AuthAuthLogResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorAuthLogModel $data
         */

        return [
            "envInfo" => $data->env_info,
            "savedLogin" => $data->cached_login,
            "authType" => $data->auth_type->name,
            "date" => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}