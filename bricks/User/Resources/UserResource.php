<?php

namespace Elevator\User\Resources;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\User\Model\ElevatorProfileModel;

/**
 * @property string $id
 * @property string $email
 * @property string $name
 * @property string|null $about
 * @property string|null $dp
 * @property string $role
 * @property string $roleId
 * @property bool $isSuper
 * @property array $permissions
 * @property array $xPermissions
 * @property bool $isActive
 * @property string $position
 * @property string $positionId
 */
class UserResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorProfileModel $data
         */

        return [
            'id' => $data->id,
            'email' => $data->email,
            'name' => $data->concat_name(),
            'firstName' => $data->first_name,
            'lastName' => $data->last_name,
            'about' => $data->about,
            'dp' => $data->dp_src,
            'role' => $data->role_name,
            'roleId' => $data->role,
            'isSuper' => $data->is_super,
            'permissions' => $data->permissions,
            'xPermissions' => $data->x_permissions,
            'isActive' => !$data->reset_login_pass,
            'positionId' => $data->position,
            'position' => $data->position_name,
            'dateJoined' => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}
