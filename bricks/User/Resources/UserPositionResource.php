<?php

namespace Elevator\User\Resources;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\User\Model\ElevatorPositionModel;

/**
 * @property string $name
 * @property string $note
 * @property string $user
 * @property string $userId
 * @property string $date
 */
class UserPositionResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorPositionModel $data
         */
        return [
            'id' => $data->id,
            'name' => $data->name,
            'note' => $data->note,
            'user' => $data->first_name . " " . $data->last_name,
            'userId' => $data->created_by,
            'date' => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}
