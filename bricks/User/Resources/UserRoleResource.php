<?php

namespace Elevator\User\Resources;

use Brick<PERSON>ayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\User\Model\ElevatorRoleModel;

/**
 * @property string $roleId
 * @property string $name
 * @property string $note
 * @property array $permissions
 * @property string $user
 * @property string $userId
 * @property bool $isSuper
 * @property string $date
 */
class UserRoleResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorRoleModel $data
         */
        return [
            'roleId' => $data->id,
            'name' => $data->name,
            'note' => $data->note,
            'permissions' => $data->permissions,
            'user' => $data->first_name . " " . $data->last_name,
            'userId' => $data->created_by,
            'isSuper' => $data->is_super,
            'date' => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}
