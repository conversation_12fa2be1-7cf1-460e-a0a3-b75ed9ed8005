<?php

namespace Elevator\User\Resources;

use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\User\Model\ElevatorActivityLogModel;

/**
 * @property string $logId
 * @property string $message
 * @property string $user
 * @property array $dataset
 * @property string $date
 */
class UserActivityResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorActivityLogModel $data
         */

        return [
            'logId' => $data->id,
            'message' => $data->message,
            'user' => $data->first_name . ' ' . $data->last_name,
            'userId' => $data->user_id,
            'dataset' => $data->dataset,
            'date' => LayDate::date($data->created_at, format_index: 3),
        ];
    }
}
