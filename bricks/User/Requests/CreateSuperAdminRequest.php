<?php

namespace Elevator\User\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;

/**
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $password
 */
class CreateSuperAdminRequest extends RequestHelper
{
    protected function rules(): void
    {
        $this->vcm([ 'field' => 'first_name', "is_name" => true ]);
        $this->vcm([ 'field' => 'last_name', "is_name" => true ]);
        $this->vcm([ 'field' => 'email', 'is_email' => true]);
        $this->vcm([ 'field' => 'password', 'min_length' => 8, ]);
        $this->vcm([
            'field' => 'password_confirm',
            'required' => false,
            'match' => [
                "field" => "password",
                "message" => "Passwords must match. Please check the passwords and try again!"
            ],
        ]);
    }

}