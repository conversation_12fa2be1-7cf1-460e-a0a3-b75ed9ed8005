<?php

namespace Elevator\User\Requests;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string first_name
 * @property string last_name
 * @property string email
 * @property string role
 * @property string position
 */
class CreateNormalAdminRequest extends RequestHelper
{
    protected function rules(): void
    {
        $foundation = ElevatorFoundation::new();

        $this->vcm([ 'field' => 'first_name', "is_name" => true ]);
        $this->vcm([ 'field' => 'last_name', "is_name" => true ]);
        $this->vcm([ 'field' => 'email', 'is_email' => true]);
        $this->vcm([ 'field' => 'role', 'must_validate' => [
            "fun" => fn($v) => $foundation->user_role()->model()->fill($v)->exists(),
            "message" => "An invalid role was given"
        ]]);
        $this->vcm([ 'field' => 'position', 'must_validate' => [
            "fun" => fn($v) => $foundation->user_position()->model()->fill($v)->exists(),
            "message" => "An invalid position was given"
        ]]);

    }

}