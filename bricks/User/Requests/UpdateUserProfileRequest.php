<?php

namespace Elevator\User\Requests;

use <PERSON><PERSON><PERSON>er\Lay\Libs\FileUpload\Enums\FileUploadExtension;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string $email
 * @property string $password
 * @property bool $remember
 */
class UpdateUserProfileRequest extends RequestHelper
{
    private static ElevatorFoundation $foundation;

    protected function pre_validate(): void
    {
        self::vcm_start(self::request(), [
            'required' => false,
        ]);

        self::$foundation = ElevatorFoundation::new();
    }

    protected function rules(): void
    {
        $file_type = self::$foundation->file_store_type()->type_by_slug("user-dp");
        $current_user = self::$foundation->user_profile()->model()->fill_current_user();

        $this->vcm([
            "field" => "dp",
            'is_file' => true,
            'new_file_name' => 'dp',
            "max_size" => $file_type->max_size,
            "allowed_types" => $file_type->allowed_types,
            "sub_dir" => "users" . DIRECTORY_SEPARATOR . $current_user->profile_id,
            "bucket_url" => static::$foundation->bucket_url(),
            "pre_upload" => fn (?string $tmp_file, ?array $file) => self::$foundation->file_store()->check_duplicate($tmp_file, $file),
            "post_upload" => fn (array $response) => self::$foundation->file_store()
                ->new_file($response, [
                    "name" => $current_user->concat_name() . " DP",
                    "provider" => FileModelProviderDefault::USER_PROFILE,
                    "is_public" => true
                ])
        ]);

        $this->vcm(["field" => "about", 'clean' => [ "escape" => EscapeType::TRIM_ESCAPE ]]);

        $this->vcm(["field" => "role", 'must_validate' => [
            "fun" => fn($v) => self::$foundation->user_role()->model()->fill($v)->exists(),
            "message" => "An invalid role was received"
        ]]);

        $this->vcm(["field" => "position", 'must_validate' => [
            "fun" => fn($v) => self::$foundation->user_position()->model()->fill($v)->exists(),
            "message" => "An invalid position was received"
        ]]);

    }

}