<?php

namespace Elevator\User\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;

/**
 * @property string $email
 * @property string $password
 * @property bool $remember
 */
class UserLoginRequest extends RequestHelper
{

    protected function rules(): void
    {
        $this->vcm(["field" => "email", 'is_email' => true]);
        $this->vcm(["field" => "password", 'min_length' => 8]);
        $this->vcm(["field" => "remember", 'required' => false, ]);
    }

}