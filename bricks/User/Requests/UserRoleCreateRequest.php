<?php

namespace Elevator\User\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\PermissionManager;

/**
 * @property string $name
 * @property string $note
 * @property array $permissions
 */
class UserRoleCreateRequest extends RequestHelper
{

    protected function rules(): void
    {
        $this->vcm([ "field" => "name", ]);
        $this->vcm([ "field" => "note", 'max_length' => 300 ]);
        $this->vcm([
            "field" => "permissions[]", "json_encode" => false,
            "after_clean" => fn($v) => json_encode(PermissionManager::unique_access($v))
        ]);
    }

}
