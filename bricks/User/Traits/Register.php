<?php
declare(strict_types=1);

namespace Elevator\User\Traits;

use <PERSON><PERSON>ayer\Lay\Libs\ID\Gen;
use <PERSON>Layer\Lay\Libs\LayCrypt\LayCrypt;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Orm\SQL;
use Elevator\User\Enums\AuthTypes;
use Elevator\User\Model\ElevatorAuthModel;

trait Register
{
    /**
     * @param array{
     *     login_name: string,
     *     login_pass: string,
     *     created_by?: string,
     *     reset_login_pass?: bool,
     *     is_active?: int
     * } $columns
     * @return array{
     *     code: int,
     *     message: string,
     *     data?: array{
     *         auth: ElevatorAuthModel
     *     }
     * }
     */
    public function register_email_password(array $columns) : array
    {
        self::cleanse($columns['login_name']);

        $auth = $this->model();

        if($auth->is_duplicate($columns))
            return self::res_warning("Email exists! already");

        $id = SQL::new()->uuid();

        $auth->add([
            "id" => $id,
            "login_name" => $columns['login_name'],
            "login_pass" => LayCrypt::hash($columns['login_pass']),
            "created_by" => $columns['created_by'] ?? $id,
            "is_active" => (int) ($columns['is_active'] ?? 0),
            "reset_login_pass" => (int) ($columns['reset_login_pass'] ?? false),
        ]);

        if($auth->is_empty())
            return self::res_warning("Could not register user at the moment");

        return self::res_success("Registration successful", [
            "auth" => $auth
        ]);
    }


    /**
     * ## Generate a token and a default password when registering.
     * Dev can then send either of them to the user via email to complete their registration.
     *
     * @param array{
     *     client_id: string,
     *     login_name: string,
     *     created_by: string,
     * } $user_data
     * @return array{
     *     code: int,
     *     message: string,
     *     data: array{
     *         auth_type: AuthTypes,
     *         auth: ElevatorAuthModel,
     *         gen_pass: string,
     *     }
     * }
     */
    public function register_email_gen_pass(array $user_data) : array
    {
        $login_name = $user_data['login_name'];

        self::cleanse($login_name);

        $auth = $this->model()->by_login_name($login_name);

        if($auth->exists())
            return self::res_warning("User exists already");

        $pass = Gen::new()->length(8)->string();
        $login_pass = LayCrypt::hash($pass);

        $test = $auth->add([
            "id" => "uuid()",
            "login_name" => $login_name,
            "login_pass" => $login_pass,
            "is_active" => '1',
            "reset_login_pass" => 1,
            "created_by" => $user_data['created_by'],
            "created_at" => LayDate::now(),
        ]);

        if($test->is_empty())
            return self::res_warning("Could not register user at the moment");

        return self::res_success("Registration successful", [
            "auth_type" => AuthTypes::EMAIL_PASSWORD,
            "auth" => $auth,
            "gen_pass" => $pass,
        ]);
    }

}
