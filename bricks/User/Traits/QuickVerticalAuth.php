<?php

namespace Elevator\User\Traits;

use <PERSON><PERSON><PERSON>er\Lay\Core\Api\Enums\ApiStatus;
use <PERSON><PERSON>ayer\Lay\Libs\ID\Gen;
use BrickLayer\Lay\Libs\LayCrypt\LayCrypt;
use BrickLayer\Lay\Orm\SQL;
use Elevator\User\Model\ElevatorAuthModel;
use Elevator\User\Requests\CreateNormalAdminRequest;
use Elevator\User\Requests\CreateSuperAdminRequest;
use Elevator\User\Requests\UserLoginRequest;
use Elevator\User\Requests\UserNewPasswordRequest;
use Elevator\User\Requests\UserPasswordResetRequest;
use Elevator\User\Resources\UserResource;
use Elevator\Utils\Enums\LogActivityTypes;

trait QuickVerticalAuth
{
    public function login_with_session() : array
    {
        $request = new UserLoginRequest();

        if($request->error)
            return self::res_warning($request->error);

        $login = $this->login_email_password([
            "login_name" => $request->email,
            "login_pass" => $request->password,
            "login_saved" => $request->remember,
        ]);

        if(!ApiStatus::is_ok($login['code']))
            return $login;

        /**
         * @var ElevatorAuthModel $auth
         */
        $auth = $login['data']['auth'];

        if(!$auth->is_active)
            return self::res_warning(
                "User's account has not been activated, please check your email for the activation mail"
            );

        $profile = $this->profile_model()->by_auth_id($auth->id);

        if($profile->is_empty())
            return self::res_warning("Profile does not exist, please contact your admin");

        self::auth_session()::start_session($profile, $auth);

        $this->record_login($profile->id);

        return self::res_success("Login successful, redirecting you to dashboard", [
            "token" => self::auth_session()::get('csrf')
        ]);
    }

    public function login_with_jwt() : array
    {
        $request = new UserLoginRequest();

        if($request->error)
            return self::res_warning($request->error);

        $login = $this->login_email_password([
            "login_name" => $request->email,
            "login_pass" => $request->password,
            "login_saved" => $request->remember,
        ]);

        if(!ApiStatus::is_ok($login['code']))
            return $login;

        /**
         * @var ElevatorAuthModel $auth
         */
        $auth = $login['data']['auth'];

        if(!$auth->is_active)
            return self::res_warning(
                "User's account has not been activated, please check your email for the activation mail"
            );

        $profile = $this->profile_model()->by_auth_id($auth->id);

        if($profile->is_empty())
            return self::res_warning("Profile does not exist, please contact your admin");

        $this->record_login($profile->id);

        return self::res_success("Login successful, redirecting you to dashboard", [
            'user' => (new UserResource($profile))->props(),
            'token' => self::auth_session()::start_jwt($profile)
        ]);
    }

    public function register_super_admin() : array
    {
        $request = new CreateSuperAdminRequest();

        if($request->error)
            return self::res_warning($request->error);

        return SQL::scoped_transaction(function () use ($request) {
            $role = $this->role_ctrl()->model()->get_super_role();

            $reg = $this->register_email_password([
                "login_name" => $request->email,
                "login_pass" => $request->password,
                "is_active" => 1,
            ]);

            if(!ApiStatus::is_ok($reg['code']))
                return $reg;

            /**
             * @var ElevatorAuthModel $auth
             */
            $auth = $reg['data']['auth'];

            if($role->is_empty()) $role->new_super_role($auth->id);

            $position = self::foundation()->user_position()->model()->get_super_position();

            if($position->is_empty())
                $position->new_super_position($auth->id);

            $user = $this->profile_model();

            if(count($user->all_by_col("role", $role->id)) >= self::foundation()->super_user()['max'])
                return self::res_warning("You cannot register like this, please contact your admin to register you");

            $user->new_profile([
                "auth_id" => $auth->id,
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "role" => $role->id,
                "position" => $position->id,
                "created_by" => $auth->id
            ]);

            if($user->is_empty())
                return self::res_warning("Could not create account now, please try again later");

            $this->activity_log(
                LogActivityTypes::CREATE,
                "New super admin created! $request->first_name $request->last_name, $request->email",
                created_by: static::auth_session()::current_user() ?? $auth->id
            );

            return self::res_success("A new super account has been created successfully");
        })['data'];
    }

    public function register_normal_admin() : array
    {
        $request = new CreateNormalAdminRequest();

        if($request->error)
            return self::res_warning($request->error);

        return SQL::scoped_transaction(function () use ($request) {
            $reg = $this->register_email_gen_pass([
                "login_name" => $request->email,
                "created_by" => static::auth_session()::current_user()
            ]);

            if(!ApiStatus::is_ok($reg['code']))
                return $reg;

            $user = $this->profile_model()->new_profile([
                "auth_id" => $reg['data']['auth']->id,
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "role" => $request->role,
                "position" => $request->position,
            ]);

            if($user->is_empty())
                return self::res_warning("Could not create account now, please try again later");

            $this->activity_log(
                LogActivityTypes::CREATE,
                "New user created! $request->first_name $request->last_name, $request->email"
            );

            static::foundation()->mailer()->welcome_admin([
                "email" => $request->email,
                "name" => $request->first_name . ' ' . $request->last_name,
                "password" => $reg['data']['gen_pass'],
            ]);

            return self::res_success("A new regular account has been created successfully");
        })['data'];
    }

    public function resend_register_link() : array
    {
        $id = self::clean(self::request()->id);
        $user = $this->profile_model()->fill($id);

        if($user->is_empty())
            return self::res_warning("An Invalid user was received!");

        $auth = $this->model()->fill($user->auth_id);

        if($auth->is_empty())
            return self::res_warning("An Invalid user was received!");

        if(!$auth->reset_login_pass)
            return self::res_success("User active already, cannot complete operation!");

        $pass = Gen::new()->length(8)->string();
        $login_pass = LayCrypt::hash($pass);

        $auth->edit_self([
            "login_pass" => $login_pass
        ]);

        static::foundation()->mailer()->welcome_admin([
            "email" => $auth->login_name,
            "name" => $user->first_name . " " . $user->last_name,
            "password" => $pass,
        ]);

        return self::res_success("Registration email resent. Please check your inbox or spam for the code");
    }

    public function change_password() : array
    {
        $request = self::request();

        if(!$request?->password)
            return self::res_warning("Password is required!");

        if(strlen($request->password) < 8)
            return self::res_warning("Password length must be at least 8 characters");

        $x = $this->update_via_old_password([
            "auth_id" => self::foundation()->user_profile()->model()->fill_current_user()->auth_id,
            "old_password" => $request->old_password,
            "login_pass" => $request->password,
        ]);

        static::auth_session()::unset_session("must_reset_password");

        return $x;
    }

    public function delete() : array
    {
        $id = self::clean(self::request()->id);

        $user = $this->profile_model()->fill($id);

        if($user->is_empty())
            return self::res_success("User does not exist or may have been deleted already, please reload this page");

        $post = $this->model()->fill($user->auth_id);

        if($post->is_empty() || !($post->delete_self() && $user->delete_self()))
            return self::res_success("User does not exist or may have been deleted already, please reload this page");

        $this->activity_log(
            LogActivityTypes::DELETE,
            "User [$post->id] was deleted",
        );

        return self::res_success( "User deleted successfully!");
    }

    public function reset_password_outside_session() : array
    {
        $request = new UserPasswordResetRequest();

        if($request->error)
            return self::res_warning($request->error);

        return $this->password_reset_via_email([
            "login_name" => $request->email,
            "name" => "User"
        ]);
    }

    public function set_new_password_outside_session(string $token) : array
    {
        $request = new UserNewPasswordRequest();

        if($request->error)
            return self::res_warning($request->error);

        return $this->update_password_via_token([
            "password" => $request->password,
            "token" => $token
        ]);
    }
}