<?php
declare(strict_types=1);

namespace Elevator\User\Traits;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayCookieStorage;
use BrickLayer\Lay\Libs\LayCrypt\LayCrypt;
use Elevator\User\Enums\AuthTypes;
use Elevator\User\Model\ElevatorAuthLogModel;
use Elevator\User\Model\ElevatorAuthModel;

trait Login
{
    private AuthTypes $auth_type;
    private string $auth_id;
    private bool $login_saved;

    /**
     * @param array{
     *     login_name: string,
     *     login_pass: string,
     *     login_saved: bool
     * } $data
     * @return array{
     *     code: int,
     *     message: string,
     *     data: array{
     *         auth: ElevatorAuthModel
     *     }
     * }
     */
    public function login_email_password(array $data) : array
    {
        $auth = $this->model()->by_login_name($data['login_name']);

        if($auth->is_empty() || !LayCrypt::verify($data['login_pass'], $auth->login_pass))
            return self::res_warning("Invalid login credentials");

        $this->auth_type = AuthTypes::EMAIL_PASSWORD;
        $this->auth_id = $auth->id;
        $this->login_saved = filter_var($data['login_saved'] ?? false, FILTER_VALIDATE_BOOL);

        if ($this->login_saved)
            LayCookieStorage::save_to_db($auth->id);

        return self::res_success("Login successful", data: [
            "auth" => $auth
        ]);
    }

    /**
     * This method records the time of login with additional metadata into an ElevatorAuthLogModel model.
     * The response can be gotten by calling the static property ::$response.
     *
     * @param string $profile_id
     * @return ElevatorAuthLogModel
     * @example ElevatorAuthLogModel::log($this->user_id, $this->auth_type, $this->user_type, $this->login_saved)
     */
    protected function record_login(string $profile_id) : ElevatorAuthLogModel
    {
        return static::foundation()->user_auth_log()->model()->log(
            $this->auth_id,
            $profile_id,
            $this->auth_type,
            $this->login_saved
        );
    }
    
}
