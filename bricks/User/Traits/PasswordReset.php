<?php
declare(strict_types=1);

namespace Elevator\User\Traits;

use <PERSON><PERSON><PERSON>er\Lay\Libs\ID\Gen;
use <PERSON><PERSON>ayer\Lay\Libs\LayCrypt\LayCrypt;
use BrickLayer\Lay\Libs\LayDate;

trait PasswordReset
{
    /**
     * Send password reset email to a user
     *
     * @param array{
     *     login_name: string,
     *     name: string,
     *     expires: string,
     * } $user_data
     * @return array{
     *     code: int,
     *     message: string,
     *     data: null
     * }
     */
    public function password_reset_via_email(array $user_data) : array
    {
        $login_name = $user_data['login_name'];

        self::cleanse($login_name);

        $auth = $this->model()->by_login_name($login_name);

        if($auth->is_empty())
            return self::res_warning("No user found with that email");

        $token = Gen::uuid(24);
        $expires = LayDate::unix($user_data['expires'] ?? "1 hour");

        $test = $auth->edit_self([
            "otp_token" => $token,
            "otp_token_expires" => $expires,
        ]);

        if(!$test)
            return self::res_warning("Could not send reset email at the moment, please try again later");

        $sent = static::foundation()->mailer()->reset_password([
            "email" => $auth->login_name,
            "name" => $user_data['name'],
            "token" => $token,
            "expires" => $expires
        ]);

        if($sent)
            return self::res_success("A password reset email has been sent to your email address");

        return self::res_warning("Could not send password reset mail at the moment, please try again later");
    }

    /**
     * After an email has been sent to a user. The process of updating the new password via a special token
     *
     * @param array{
     *     client_id: string,
     *     token: string,
     *     password: string,
     * } $user_data
     * @return array{
     *     status: 'success' | 'warning',
     *     code: int,
     *     message: string,
     *     data: array{
     *      user_id: string
     *     },
     * }
     */
    public function update_password_via_token(array $user_data) : array
    {
        $user = $this->model()->by_token($user_data['token']);

        if ($user->is_empty() && !$user->otp_token_expires)
            return self::res_warning("Invalid token received!");

        if (LayDate::expired($user->otp_token_expires))
            return self::res_warning( "Token has expired! Please restart the process");

        $update = $user->edit_self([
            "otp_token" => null,
            "otp_token_expires" => null,
            "login_pass" => LayCrypt::hash($user_data['password']),
            "is_active" => 1
        ]);

        if($update)
            return self::res_success("New password created successfully");

        return self::res_warning();
    }

    /**
     * Change the password using old password.
     * This can be useful when dev wants to change it in a session.
     * The session should be validated before calling this function
     *
     * @param array{
     *     auth_id: string,
     *     old_password: string,
     *     login_pass: string,
     * } $user_data
     * @return array
     */
    public function update_via_old_password(array $user_data) : array
    {
        $auth = $this->model()->fill($user_data['auth_id']);

        if ($auth->is_empty())
            return self::res_warning("User is invalid!");

        if(strlen($user_data['login_pass']) < 8)
            return self::res_warning("Password must be at least 8 characters long");

        if(!LayCrypt::verify($user_data['old_password'], $auth->login_pass))
            return self::res_warning("Old password is incorrect");

        $update = $auth->edit_self([
            "login_pass" => LayCrypt::hash($user_data['login_pass']),
            "reset_login_pass" => '0'
        ]);

        if($update)
            return self::res_success("Password updated successfully");

        return self::res_warning();
    }

}
