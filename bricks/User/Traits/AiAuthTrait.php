<?php

namespace Elevator\User\Traits;

use Brick<PERSON>ayer\Lay\Core\LayException;
use Elevator\Utils\Enums\LogActivityTypes;

trait AiAuthTrait
{
    public function register_ai_admin(string $name) : array
    {
        $user = $this->model();

        if($user->is_duplicate([ "login_name" => $name ]))
            return self::res_warning("System Credential exists already");

        $user->add([
            "login_name" => $name,
            "is_bot" => "1",
        ]);

        if($user->is_empty())
            return self::res_warning("Could not create AI user at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "New AI Credential was created! $name, $user->id"
        );

        return self::res_success("A new AI user has been created successfully", [
            "auth_id" => $user->id
        ]);
    }

    public function ai_admin_is_valid(string $id) : bool
    {
        return $this->model()->fill(self::clean($id))->exists();
    }

    public function ai_login(string $profile_id) : void
    {
        $profile = $this->ai_agent_ctrl()->model()->fill($profile_id);

        if($profile->is_empty()) {
            LayException::throw("Trying to login as an AI Agent, but profile ID $profile_id does not exist");
        }

        $auth = $this->model()->fill($profile->auth_id);
        $role = $this->role_ctrl()->model()->fill($profile->role);

        $session = static::auth_session();

        $session::update_session('email', $auth->login_name);
        $session::update_session('auth_id', $auth->id);
        $session::update_session('user', $profile->props());
        $session::update_session('is_ai_user', true);
        $session::update_session('is_super_admin', $role->is_super);

        $session::update_session('permissions', [...$profile->x_permissions, ...$role->permissions]);
    }

}