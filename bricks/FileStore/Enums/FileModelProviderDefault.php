<?php

namespace Elevator\FileStore\Enums;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Enums\EnumHelper;
use Elevator\Blog\Model\ElevatorPostModel;
use Elevator\Brand\Model\ElevatorBrandModel;
use Elevator\Brand\Model\ElevatorBrandPartnerModel;
use Elevator\Brand\Model\ElevatorBrandTeamModel;
use Elevator\Brand\Model\ElevatorBrandTestimonyModel;
use Elevator\User\Model\ElevatorProfileModel;

enum FileModelProviderDefault : string implements FileModelProvider
{
    case BLOG_POST = ElevatorPostModel::class;
    case USER_PROFILE = ElevatorProfileModel::class;
    case BRAND = ElevatorBrandModel::class;
    case BRAND_TESTIMONY = ElevatorBrandTestimonyModel::class;
    case BRAND_PARTNER = ElevatorBrandPartnerModel::class;
    case BRAND_TEAM = ElevatorBrandTeamModel::class;

    use EnumHelper;

    public function provider(): self
    {
        return $this;
    }
}
