<?php

namespace Elevator\FileStore\Enums;

use UnitEnum;

interface FileModelProvider extends UnitEnum
{
    /**
     * Just return $this.
     *
     * This interface is used to mark an Enum class that's implementing this interface
     *
     * @return self
     */
    public function provider() : self;

    public static function to_enum(string $value, bool $throw_error = false, bool $use_value = true) : ?self;

    // use EnumUtil;
}