<?php

namespace Elevator\FileStore\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\FileStore\Model\ElevatorFileStoreModel;

/**
 * @property string $id
 * @property string $name
 * @property string $url
 * @property string $size
 * @property array $metadata
 */
class FileResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorFileStoreModel $data
         */
        return [
            "id" => $data->id,
            "name" => $data->name,
            "url" => $data->url,
            "size" => $data->size,
            "metadata" => $data->metadata,
        ];
    }
}