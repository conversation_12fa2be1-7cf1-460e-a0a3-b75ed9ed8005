<?php
declare(strict_types=1);

namespace Elevator\FileStore\Model;

use <PERSON><PERSON><PERSON>er\Lay\Libs\FileUpload\Enums\FileUploadStorage;
use BrickLayer\Lay\Libs\FileUpload\Enums\FileUploadType;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\FileStore\Enums\BucketFileStatus;
use Elevator\FileStore\Enums\FileModelProvider;
use Elevator\FileStore\Enums\FileModelProviderDefault;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string|null $name
 * @property string $path // File path
 * @property string $url // Signed URL
 * @property string|null $type_id // Project file type. E.g: invoice, user_dp, student_signature
 * @property string $mime_type
 * @property int $size
 * @property string $checksum_algo
 * @property string $checksum_tmp
 * @property string $checksum
 * @property string $extension
 * @property FileUploadStorage $storage
 * @property FileUploadType $upload_type
 * @property array{
 *     dimension: array{
 *         width: int,
 *         height: int,
 *     },
 * } $metadata
 * @property bool $is_public
 * @property int|null $expires_at
 * @property BucketFileStatus $status
 * @property FileModelProvider $provider // The model responsible for the file upload. This should be an enum of Models that can upload files
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorFileStoreModel extends BaseModelHelper
{
    public static string $table = "elevator_file_stores";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("is_public", "bool", true);
        $this->cast("metadata", "array", []);
        $this->cast("provider", FileModelProvider::class, default_value: null, parser: fn($v) => FileModelProviderDefault::to_enum($v));
        $this->cast("upload_type", FileUploadType::class, default_value: null, parser: fn($v) => FileUploadType::to_enum($v));
        $this->cast("storage", FileUploadStorage::class, default_value: null, parser: fn($v) => FileUploadStorage::to_enum($v));
        $this->cast("status", BucketFileStatus::class, default_value: null, parser: fn($v) => BucketFileStatus::to_enum($v));
    }

    protected function resolve_conflict(SQL $db) : void
    {
        $db->on_conflict(
            unique_columns: ['client_id', 'checksum_tmp'],
            action: 'UPDATE'
        );
    }

    public function new_file(array|RequestHelper $columns) : self
    {
        return $this->add($columns, true);
    }

    public function by_checksum(string $checksum, ?string $client_id = null) : self
    {
        return $this
            ->pre_run(fn(SQL $db) => $db
                ->or_where("checksum_tmp", $checksum)
                ->and_where("client_id", $client_id ?? $this->client_id())
            )
            ->get_by("checksum", $checksum);
    }

    /**
     * @return null|array{
     *     width: int,
     *     height: int,
     * }
     */
    public function dimension() : ?array
    {
        if(isset($this->metadata['dimension']['width']))
            return [
                'width' => $this->metadata['dimension']['width'],
                'height' => $this->metadata['dimension']['height'],
            ];

        return null;
    }
}
