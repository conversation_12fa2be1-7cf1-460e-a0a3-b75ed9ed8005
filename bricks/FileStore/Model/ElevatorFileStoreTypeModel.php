<?php
declare(strict_types=1);

namespace Elevator\FileStore\Model;

use BrickLayer\Lay\Libs\FileUpload\Enums\FileUploadExtension;
use BrickLayer\Lay\Libs\ID\Gen;
use BrickLayer\Lay\Libs\LayArray;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $name
 * @property string $slug
 * @property int|null $max_size
 * @property array|null $max_dimension
 * @property array|null $allowed_types
 * @property bool $can_delete
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorFileStoreTypeModel extends BaseModelHelper
{
    public static string $table = "elevator_file_store_types";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("max_dimension", "array", null);
        $this->cast("can_delete", "bool", false);
        $this->cast("allowed_types", "array", null, fn($x) => LayArray::map(
            json_decode($x, true),
            fn($v) => FileUploadExtension::to_enum($v, use_value: false))
        );
    }

    protected function resolve_conflict(SQL $db) : void
    {
        $db->on_conflict(
            unique_columns: ['client_id', 'name'],
            ignore_columns: ['brand_id'],
            action: 'UPDATE'
        );
    }

    public function is_duplicate(array|RequestHelper $columns): bool
    {
        $columns = $this->req_2_array($columns);

        return $this
            ->pre_run(
                fn(SQL $db) => $db
                    ->and_where("client_id", $columns['client_id'] ?? $this->client_id())
            )
            ->get_by("name", $columns['name'])->exists();
    }

    public function new_type(array|RequestHelper $columns) : self
    {
        if($this->is_duplicate($columns)) return $this;

        $columns['slug'] = Gen::new()
            ->db_confirm(
                static::$table,
                "slug",
                fn (SQL $db) => $db
                    ->and_where("client_id", $columns['client_id'] ?? $this->client_id())
            )
            ->slug($columns['name']);

        return $this->add($columns);
    }

    public function by_slug(string $slug, ?string $client_id = null) : self
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $client_id ?? $this->client_id()))
            ->get_by("slug", $slug);
    }

    public function client_n_defaults(?string $client_id = null) : array
    {
        return $this
            ->pre_run(
                fn(SQL $db) => $db->or_where("client_id", $client_id)
            )
            ->all_by_col("client_id", $this->client_id());
    }

}
