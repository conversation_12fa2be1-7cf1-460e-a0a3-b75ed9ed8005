<?php
declare(strict_types=1);

namespace Elevator\FileStore\Controller;

use Elevator\FileStore\Model\ElevatorFileStoreTypeModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorFileStoreTypeController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::FileStore;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorFileStoreTypeModel
    {
        return new ElevatorFileStoreTypeModel();
    }

    public function type_by_slug(string $slug) : ElevatorFileStoreTypeModel
    {
        return $this->model()->by_slug(self::clean($slug));
    }
}
