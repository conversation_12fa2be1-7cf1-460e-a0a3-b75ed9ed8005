<?php
declare(strict_types=1);

namespace Elevator\FileStore\Controller;

use BrickLayer\Lay\Libs\FileUpload\FileUpload;
use BrickLayer\Lay\Libs\LayCrypt\Enums\HashType;
use Elevator\FileStore\Enums\FileModelProvider;
use Elevator\FileStore\Model\ElevatorFileStoreModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorFileStoreController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::FileStore;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorFileStoreModel
    {
        return new ElevatorFileStoreModel();
    }

    public function hash(string $file_name, HashType $algo = HashType::MD5) : array
    {
        return (new FileUpload)->checksum($file_name, $algo);
    }

    public function by_checksum(string $checksum) : ElevatorFileStoreModel
    {
        return $this->model()->by_checksum(self::clean($checksum));
    }

    /**
     * @param string $tmp_file Temporary upload file
     * @param array|null $file File array
     * @return array|true
     */
    public function check_duplicate(string $tmp_file, ?array $file) : array|true
    {
        $store = static::foundation()->file_store();

        $hash = $store->hash($tmp_file);

        $old_file = $store->by_checksum($hash['checksum']);

        if($old_file->is_empty())
            return true;

        $algo = HashType::to_enum($old_file->checksum_algo, use_value: false);

        return [
            "uploaded" => true,
            "file_store_id" => $old_file->id,
            "url" => $old_file->url,
            "path" => $old_file->path,
            "mime_type" => $old_file->mime_type,
            "storage" => $old_file->storage,
            "extension" => $old_file->extension,
            "upload_type" => $old_file->upload_type,
            "checksum" => [
                'tmp' => [
                    'checksum' => $old_file->checksum_tmp,
                    'algo' => $algo,
                ],
                'new' => [
                    'checksum' => $old_file->checksum,
                    'algo' => $algo,
                ],
            ],
            'size' => $old_file->size,
            'width' => $old_file->dimension()['width'] ?? null,
            'height' => $old_file->dimension()['height'] ?? null,
        ];

    }

    /**
     * @param array $response
     * @param array{
     *     name: string,
     *     provider: FileModelProvider,
     *     is_public?: bool,
     * } $cols
     * @return array
     */
    public function new_file(array $response, array $cols) : array
    {
        // Is only set if a duplicate was detected
        if(isset($response["file_store_id"])) {
            $response['url'] = $response["file_store_id"]; // Always return the ID rather than the URL
            return $response;
        }

        if(!$response['uploaded'])
            return $response;

        $columns = [
            "name" => self::clean($cols['name']),
            "path" => $response['path'],
            "url" => $response['url'],
            "mime_type" => $response['mime_type'],
            "size" => $response['size'],
            "checksum_algo" => $response['checksum']['new']['algo']->name,
            "checksum_tmp" => $response['checksum']['tmp']['checksum'],
            "checksum" => $response['checksum']['new']['checksum'],
            "extension" => $response['extension'],
            "storage" => $response['storage']->name,
            "is_public" => isset($cols['is_public']) ?  ($cols['is_public'] ? '1' : '0') : '1',
            "provider" => $cols['provider']->name,
        ];

        if(isset($response['width']))
            $columns["metadata"] = json_encode([
                "dimension" => [
                    "width" => $response['width'],
                    "height" => $response['height'],
                ]
            ]);

        $store = $this->model()->new_file($columns);

        if($store->is_empty())
            return $response;

        $response['url'] = $store->id;

        return $response;
    }

}
