<?php

namespace Elevator\AiAgent\Providers;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Core\LayException;
use Elevator\AiAgent\Enums\LLM;

class Gemini extends AbstractProvider
{
    protected string $base_uri = "https://generativelanguage.googleapis.com/v1beta/models";
    protected LLM $model = LLM::GEMINI;
//    protected string $version = "2.0-flash";
    protected string $version = "2.5-flash";

    protected function payload() : array
    {
        $payload = [];

        if(!empty($this->llm_instruction)) {
            $payload["system_instruction"] = [
                "parts" => [
                    [
                        "text" => $this->llm_instruction
                    ]
                ],
            ];
        }

        $payload['contents'] = [
            [
                "parts" => [
                    [
                        "text" => $this->llm_prompt
                    ]
                ],
            ]
        ];

        $payload["generationConfig"] = [
            "temperature" => $this->llm_temperature,
            "responseMimeType" => $this->llm_response_type->value,
            "topK" => $this->llm_top_k,
            "topP" => $this->llm_top_p
        ];

        if(isset($this->llm_max_output_token))
            $payload['generationConfig']['maxOutputTokens'] = $this->llm_max_output_token;

        if(isset($this->llm_response_schema)) {
            $schema = [
                "type" => "OBJECT",
                "properties" => $this->llm_response_schema,
            ];

            if ($this->llm_response_schema_desc)
                $schema["description"] = $this->llm_response_schema_desc;

            if ($this->required_schema_keys)
                $schema["required"] = $this->required_schema_keys;

            $payload["generationConfig"]['responseSchema'] = [ "type" => "ARRAY", "items" => $schema, ];
        }

        if(isset($this->llm_safety_settings)) {
            $payload["safetySettings"] = $this->llm_safety_settings;
        }

        return $payload;
    }

    protected function exec() : void
    {
        if(!$this->api_key)
            LayException::throw("No API key specified for your LLM provider " . self::class, "NoApiKey");

        $model = strtolower($this->model->name) . "-" . $this->version;

        $uri = rtrim($this->base_uri, "/") . "/" . $model . ":streamGenerateContent?alt=sse&";

        // Best to not expose the API key by mistake
        $this->uri = $uri . "key=" . substr($this->api_key, 0, 8) . '...';

        // Pass the complete api key for real
        $this->send($uri . "key=" . $this->api_key);
    }

    protected function flush_output(array $decoded) : array|null|string
    {
        // Extract the content from the response
        $text = null;

        if (isset($decoded['candidates'][0]['content']['parts'][0]['text'])) {
            $text = $decoded['candidates'][0]['content']['parts'][0]['text'];
        }

        return $text;
    }
}