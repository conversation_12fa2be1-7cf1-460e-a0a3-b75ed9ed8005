<?php

namespace Elevator\AiAgent\Providers;

use <PERSON><PERSON><PERSON>er\Lay\Core\Api\Enums\ApiStatus;
use <PERSON><PERSON>ayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Core\LayException;
use BrickLayer\Lay\Libs\Dir\LayDir;
use <PERSON>Layer\Lay\Libs\ID\Gen;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\LayFn;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use BrickLayer\Lay\Libs\ServerEvents\Events;
use CurlHandle;
use Elevator\AiAgent\Enums\LLM;
use Elevator\AiAgent\Enums\LLMResponseType;

abstract class AbstractProvider
{
    use ControllerHelper;

    private string $buffer_file;
    private string $current_stream_accumulator = ""; // Accumulates raw text parts for the *current* stream only
    protected array $final_decoded_data = []; // Stores the completely decoded JSON array ready for final save/parse

    protected bool $debug = false;

    protected ?string $gen_id = null;
    protected string $base_uri;
    protected string $uri;
    protected LLM $model;
    protected string $version;

    protected bool $close_stream = true;
    protected bool $is_prompt_complete = true;

    protected string $llm_instruction = "";
    protected string $llm_prompt;
    protected float $llm_temperature = 0.7;
    protected float $llm_top_k = 40;
    protected float $llm_top_p = 0.95;
    protected int $llm_max_output_token;
    protected LLMResponseType $llm_response_type = LLMResponseType::JSON;
    protected mixed $llm_response_schema;
    protected mixed $llm_response_schema_desc;
    protected array $required_schema_keys;
    protected array $llm_safety_settings;

    protected Events $events_instance;

    /**
     * Executes the API call to the LLM.
     * This method should prepare the URI and call the `send` method.
     */
    abstract protected function exec(): void;

    /**
     * Prepares the payload for the LLM API request.
     * @return array The request body.
     */
    abstract protected function payload(): array;

    /**
     * Extracts the relevant text content from a decoded LLM API response chunk.
     * This method is crucial for handling streamed responses where the model
     * might send JSON parts or plain text.
     * @param array $decoded The decoded JSON array of a single `data:` line from the stream.
     * @return mixed The extracted content from the chunk.
     */
    abstract protected function flush_output(array $decoded): mixed;

    /**
     * Constructor for AbstractProvider.
     * @param string|null $api_key The API key for the LLM provider. Defaults to environment variable.
     * @param int|null $timeout The cURL request timeout in seconds.
     * @param bool $buffer If true, buffers the entire response before processing. If false, streams via SSE.
     */
    public function __construct(
        protected ?string $api_key = null,
        protected ?int $timeout = 60,
        protected bool $buffer = false
    ) {
        $this->api_key ??= LayFn::env("LLM_API_KEY");
        $this->sse();
    }

    /**
     * Dumps debug information to a log file if debug mode is enabled.
     * @param mixed $out The data to dump.
     */
    private function dump_debug(mixed $out): void
    {
        if (!$this->debug) {
            return;
        }

        $dir = LayConfig::server_data()->temp . "llm-gen-debug" . DIRECTORY_SEPARATOR . LayDate::date(format: "Y-m-d") . DIRECTORY_SEPARATOR;
        LayDir::make($dir, 0777, true);

        if (!is_string($out)) {
            $out = json_encode($out, JSON_PRETTY_PRINT);
        }

        file_put_contents($dir . ($this->gen_id) . '.log', $out . "\n", FILE_APPEND);
    }

    /**
     * Returns the base directory for LLM output files.
     * Creates the directory if it doesn't exist.
     * @return string The output directory path.
     */
    private static string $out_dir;
    protected function output_dir(): string
    {
        if (!isset(self::$out_dir)) {
            self::$out_dir = LayConfig::server_data()->temp . "llm-gen-out";
            LayDir::make(self::$out_dir);
        }

        return self::$out_dir;
    }

    /**
     * Returns the full path for a generated LLM output file.
     * @param string $gen_id The generation ID.
     * @return string The file path.
     */
    protected function output_file(string $gen_id): string
    {
        return $this->output_dir() . DIRECTORY_SEPARATOR . $gen_id . ".json";
    }

    /**
     * Initializes and returns the Server-Sent Events (SSE) instance.
     * @return Events The SSE instance.
     */
    protected function sse(): Events
    {
        if (!isset($this->events_instance)) {
            $this->events_instance = new Events();

            if (!$this->buffer) {
                $this->events_instance->set_headers(false);
            }
        }

        return $this->events_instance;
    }

    /**
     * Validates if a decoded LLM response chunk has a valid structure.
     * Logs errors if the structure is invalid.
     * @param mixed $decoded The decoded JSON chunk.
     * @param mixed $raw The raw JSON string of the chunk.
     * @return bool True if valid, false otherwise.
     */
    protected function decoded_valid(mixed $decoded, mixed $raw): bool
    {
        $this->dump_debug($raw);

        if ($decoded) {
            return true;
        }

        LayException::log(
            "Request: [" . $this->uri . "] \n" .
            "Model Info: [" . implode(',', $this->info()) . "] \n" .
            "Invalid response structure was received. Check other log for structure",
            log_title: "LLMWrongStruct"
        );

        LayException::log($raw, log_title: "LLMWrongStructDisplay");

        return false;
    }

    /**
     * Callback function for cURL to process streamed data.
     * Accumulates raw text parts and sends SSE messages if not buffering.
     * @param CurlHandle $ch The cURL handle.
     * @param string $data The received data chunk.
     * @return int The number of bytes processed.
     */
    protected function stream_output($ch, string $data): int
    {
        $buffer = $data;

        $this->dump_debug("Raw Stream Data: " . $buffer);

        $stream_length = strlen($data);

        // Check for HTTP errors first
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (!ApiStatus::is_ok($code)) {
            $error = curl_error($ch) ?: "Could not complete request to: $this->uri";

            if (!$this->buffer) {
                $this->sse()->error("LLM API HTTP Error: " . $code . " - " . $error);
            }

            LayException::throw($error . "\n" . $data, "LLMError");
        }

        while (($pos = strpos($buffer, "\n")) !== false) {
            $line = substr($buffer, 0, $pos);
            $buffer = substr($buffer, $pos + 1);

            $line = trim($line);

            if (empty($line)) {
                continue;
            }

            if (str_starts_with($line, 'data: ')) {
                $raw_json_chunk = substr($line, 6); // Remove 'data: ' prefix

                // Attempt to decode the raw JSON chunk to validate its structure
                $decoded_chunk = json_decode($raw_json_chunk, true);

                if (!$this->decoded_valid($decoded_chunk, $raw_json_chunk)) {
                    if (!$this->buffer) {
                        $this->sse()->error("Could not decode response chunk, an error occurred");
                    }
                    // Break to stop processing corrupted stream further.
                    break;
                }

                // Check for error responses from the LLM within the chunk
                if (isset($decoded_chunk['error'])) {
                    $error_msg = $decoded_chunk['error']['message'] ?? 'Unknown error from LLM API';
                    if (!$this->buffer) {
                        $this->sse()->error("LLM API Error: " . $error_msg);
                    }
                    break;
                }

                // Extract the content using flush_output (which should return string like '[', '{...}', '],[' etc.)
                $content_part = $this->flush_output($decoded_chunk);

                // Accumulate the raw string parts for the *current* stream only
                $this->current_stream_accumulator .= $content_part;

                // Send chunks if not buffering
                if (!$this->buffer) {
//                    $this->sse()->message(["content" => $content_part, "type" => "text_chunk"]);
                    $this->sse()->message(["content" => "partial-text", "type" => "text_chunk"]);
                }
            }
        }

        return $stream_length;
    }

    /**
     * Sends the cURL request to the LLM API and handles the streamed response.
     * @param string $uri The full request URI including model and query parameters.
     */
    protected function send(string $uri): void
    {
        $payload = LayFn::json_encode($this->payload());

        $ch = curl_init();
        $stitch_id = $this->gen_id ?? "[" . LayDate::now() . "]-" . Gen::new()->string();
        $this->gen_id = $stitch_id;
        $this->buffer_file = $this->output_file($stitch_id); // Set buffer_file path

        // --- Step 1: Prepare previous content for stitching if gen_id is active ---
        $previous_decoded_data = []; // Reset for each send call

        if ($this->gen_id && file_exists($this->buffer_file)) {
            $loaded_raw_content = file_get_contents($this->buffer_file);

            if (!empty($loaded_raw_content)) {
                $decoded_previous = json_decode($loaded_raw_content, true);


                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_previous)) {
                    $previous_decoded_data = $decoded_previous;
                } else {
                    LayException::log(
                        "Failed to decode existing content for stitching: " . json_last_error_msg(),
                        log_title: "LLMStitchError",
                    );

                    // Optionally, clear the file if it's corrupted
                    file_put_contents($this->buffer_file, '');
                }
            }
        }

        $this->current_stream_accumulator = ""; // Ensure this is clear before starting stream


        $headers = [
            'Content-Type: ' . $this->llm_response_type->value,
            'User-Agent: Lay/Elevator',
            'Accept: text/event-stream',
            'Cache-Control: no-cache',
            'Connection: keep-alive'
        ];


        if ($this->model !== LLM::GEMINI && $this->api_key) {
            $headers[] = 'Authorization: Bearer ' . $this->api_key;
        }

        curl_setopt_array($ch, [
            CURLOPT_URL => $uri,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => $headers,

            CURLOPT_RETURNTRANSFER => false,
            CURLOPT_WRITEFUNCTION => [$this, "stream_output"],

            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HEADER => false,
            CURLOPT_BUFFERSIZE => 1024,
            CURLOPT_TCP_NODELAY => true,
            CURLOPT_FRESH_CONNECT => true,
            CURLOPT_FORBID_REUSE => true,

            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_NOPROGRESS => true,
        ]);

        $result = curl_exec($ch);
        $error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($result === false || !ApiStatus::is_ok($http_code)) {
            $error = 'Gateway Error: ' . $error;

            if (!$this->buffer) {
                $this->sse()->error($error, status: $http_code);
            } else {
                LayException::throw($error, "LLMError");
            }

            return;
        }

        // --- Step 2: Process the current stream accumulator into a valid JSON array ---
        $cleaned_current_stream_string = $this->current_stream_accumulator;
        $this->dump_debug("Raw Current Stream: " . $cleaned_current_stream_string);

        // This is crucial to fix '][', '],[' or '}\n{', '}]\n[{' issues in streamed JSON arrays
        // Replace ']' followed by optional whitespace and then '[' with just ','
        $cleaned_current_stream_string = preg_replace('/\]\s*\[/', ',', $cleaned_current_stream_string);

        // Trim any leading/trailing whitespace or newlines.
        $cleaned_current_stream_string = trim($cleaned_current_stream_string);

        $newly_decoded_data = [];

        if (!empty($cleaned_current_stream_string)) {
            $this->dump_debug("Cleaned Current Stream: " . $cleaned_current_stream_string);
            $newly_decoded_data = json_decode($cleaned_current_stream_string, true);

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($newly_decoded_data)) {
                LayException::log(
                    "Error decoding current stream's JSON: " . json_last_error_msg() . "\n" .
                    "String: " . $cleaned_current_stream_string,
                    log_title: "LLMCurrentStreamDecodeError"
                );

                $newly_decoded_data = []; // Fallback to empty array on error
            }
        }

        // --- Step 3: Merge previous and new decoded data ---
        // Ensure both are arrays before merging.
        $this->final_decoded_data = array_merge($previous_decoded_data, $newly_decoded_data);


        // --- Step 4: Save the complete, valid JSON array to the file ---
        file_put_contents($this->buffer_file, json_encode($this->final_decoded_data, JSON_PRETTY_PRINT));

        if ($this->close_stream) {
            $this->is_prompt_complete = true;
            if (!$this->buffer) {
                $this->sse()->end(); // Signal the client that the stream is complete.
            }
        }
    }

    /**
     * Enables debug logging for the LLM agent.
     * Only active in development environments.
     * @return $this
     */
    public function debug(): self
    {
        if (LayConfig::$ENV_IS_DEV) {
            $this->debug = true;
        }

        return $this;
    }

    /**
     * Parses the LLM output file given its file path.
     * @param string $file The full path to the LLM output file.
     * @param bool $parse If true, returns a decoded array; otherwise, returns the raw string content.
     * @return array|string|null The parsed data or raw string, or null if the file doesn't exist or is outside the output dir.
     */
    public function parse_file(string $file, bool $parse = true): array|string|null
    {
        $dir = pathinfo($file, PATHINFO_DIRNAME);

        if ($dir != $this->output_dir()) {
            LayException::throw("Trying to parse an LLM file outside the LLM output DIR!", "WrongPathParse");
        }

        if (!file_exists($file)) {
            return null;
        }

        $out = file_get_contents($file);

        if (!$parse) {
            return $out;
        }

        return json_decode($out, true);
    }

    /**
     * Checks if a generated output file with the given ID exists.
     * @param string $gen_id The generation ID.
     * @return bool True if the file exists, false otherwise.
     */
    public function gen_exists(string $gen_id): bool
    {
        return file_exists($this->output_file($gen_id));
    }

    /**
     * Parses the LLM output file generated after the completion of a prompt using the `gen_id`.
     * @param string $gen_id The generation ID.
     * @param bool $parse If true, returns a decoded array; otherwise, returns the raw string content.
     * @return array|string|null The parsed data or raw string, or null if the generation is not complete or the file doesn't exist.
     */
    public function parse_gen(string $gen_id, bool $parse = true): array|string|null
    {
        if (!$this->gen_exists($gen_id)) {
            return null;
        }

        $out = file_get_contents($this->output_file($gen_id));

        if (!$parse) {
            return $out;
        }

        return json_decode($out, true);
    }

    /**
     * Clears all LLM output logs/files.
     */
    public function clear_logs(): void
    {
        LayDir::unlink(self::output_dir());
    }

    /**
     * Tags a particular prompt to an ID for future reference or for stitching a very long prompt.
     * When a gen_id is set, the system will attempt to load previous content
     * from the corresponding file and append new content to it.
     * @param string $id The custom ID for the generation.
     * @param bool $timestamp If true, prepends a timestamp to the ID.
     * @return $this
     */
    public function gen_id(string $id, bool $timestamp = true): self
    {
        $id = ($timestamp ? "[" . LayDate::now() . "]-" : "") . $id;

        $this->gen_id = $id; // Set the gen_id
        return $this;
    }

    /**
     * Clears the current generation ID, allowing subsequent prompts to generate new IDs.
     * Also sets `close_stream` back to true.
     */
    public function clear_gen_id(): void
    {
        $this->gen_id = null;
        $this->close_stream = true;
    }

    /**
     * Sends the actual prompt to the LLM model and triggers the generation process.
     * Resets internal accumulators for a new prompt.
     * If a `gen_id` is set, it will attempt to load previous content before starting the new stream.
     * @param string $val The prompt string to send.
     * @return string The file location where the LLM output will be stored.
     */
    public function prompt(string $val): string
    {
        $this->llm_prompt = $val;
        // Reset only current stream accumulator here, previous data is handled in send() if gen_id is active
        $this->current_stream_accumulator = "";
        $this->final_decoded_data = []; // Reset final data before a new prompt
        $this->is_prompt_complete = false;

        $this->exec(); // Execute the API call

        return $this->buffer_file; // Return the path to the file
    }

    /**
     * Change behaviour of closing stream automatically
     */
    public function autoclose(bool $close): self
    {
        $this->close_stream = $close;
        return $this;
    }

    /**
     * Explicitly closes a multipart prompt stream, signaling the SSE client that generation is complete.
     */
    public function close(): void
    {
        $this->close_stream = true;
    }

    /**
     * Returns the Server-Sent Events (SSE) client instance for direct interaction.
     * @return Events The SSE client instance.
     */
    public function sse_client(): Events
    {
        return $this->events_instance;
    }

    /**
     * Provides information about the current LLM provider setup.
     * @return array{
     *     model: string,
     *     version: string,
     *     base_uri: string,
     *     api_key: string,
     *     request_timeout: float,
     * }
     */
    public function info(): array
    {
        return [
            "model" => $this->model->name,
            "version" => $this->version,
            "base_uri" => $this->base_uri,
            "api_key" => substr($this->api_key, 0, 8) . '...', // Mask part of the API key for security
            "request_timeout" => $this->timeout,
        ];
    }

    /**
     * Sets the API key for the LLM provider.
     * @param string $api The API key.
     * @return $this
     */
    public function api_key(string $api): self
    {
        $this->api_key = $api;
        return $this;
    }

    /**
     * Sets the base URI for the LLM API.
     * @param string $val The base URI.
     * @return $this
     */
    public function base_uri(string $val): self
    {
        $this->base_uri = $val;
        return $this;
    }

    /**
     * Sets the specific model version to use.
     * @param string $version The model version (e.g., "2.0-flash").
     * @return $this
     */
    public function version(string $version): self
    {
        $this->version = $version;
        return $this;
    }

    /**
     * Sets the system instruction for the LLM.
     * @param string $val The system instruction.
     * @return $this
     */
    public function instruction(string $val): self
    {
        $this->llm_instruction = $val;
        return $this;
    }

    /**
     * Sets the generation temperature. Higher values lead to more creative outputs.
     * @param float $val The temperature (e.g., 0.7).
     * @return $this
     */
    public function temperature(float $val): self
    {
        $this->llm_temperature = $val;
        return $this;
    }

    /**
     * Sets the maximum number of output tokens for the LLM response.
     * @param int $val The maximum tokens.
     * @return $this
     */
    public function max_tokens(int $val): self
    {
        $this->llm_max_output_token = $val;
        return $this;
    }

    /**
     * Sets the top-p (nucleus sampling) parameter.
     * @param float $val The top-p value (e.g., 0.95).
     * @return $this
     */
    public function top_p(float $val): self
    {
        $this->llm_top_p = $val;
        return $this;
    }

    /**
     * Sets the top-k parameter.
     * @param float $val The top-k value (e.g., 40).
     * @return $this
     */
    public function top_k(float $val): self
    {
        $this->llm_top_k = $val;
        return $this;
    }

    /**
     * Sets the safety settings for the LLM generation.
     * @param array<int, array{category: string, threshold: string}> $data An array of safety settings.
     * @return $this
     */
    public function safety(array $data): self
    {
        $this->llm_safety_settings = $data;
        return $this;
    }

    /**
     * Sets the desired response type (MIME type) for the LLM output.
     * @param LLMResponseType $val The response type enum (e.g., LLMResponseType::JSON).
     * @return $this
     */
    public function response_type(LLMResponseType $val): self
    {
        $this->llm_response_type = $val;
        return $this;
    }

    /**
     * Sets the JSON schema for the LLM response (when response_type is JSON).
     * @param mixed $val The JSON schema definition.
     * @param string|null $description Optional description for the schema.
     * @return $this
     */
    public function response_schema(mixed $val, ?string $description = null): self
    {
        $this->llm_response_schema = $val;
        $this->llm_response_schema_desc = $description;
        return $this;
    }

    /**
     * Sets the required keys for the JSON response schema.
     * @param array $field An array of required field names.
     * @return $this
     */
    public function required_fields(array $field): self
    {
        $this->required_schema_keys = $field;
        return $this;
    }
}