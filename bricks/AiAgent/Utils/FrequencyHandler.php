<?php

namespace Elevator\AiAgent\Utils;

use BrickLayer\Lay\Libs\LayArray;
use BrickLayer\Lay\Libs\LayDate;

class FrequencyHandler
{
    private static function unit_time() : array
    {
        return [
            "id" => "TIME",
            "values" => ["12", "1"] // This is just here. The front end should send a 24 hours formatted time back
        ];
    }

    private static function unit_days() : array
    {
        return [
            "id" => "DAYS",
            "values" => [ "sun","mon","tue","wed","thu","fri","sat", ]
        ];
    }

    private static function unit_days_num() : array
    {
        return [
            "id" => "DAYS_NUM",
            "values" => [ 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31 ]
        ];
    }

    private static function unit_month() : array
    {
        return [
            "id" => "MONTH",
            "values" => ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"],
        ];
    }

    public static function validate(string $id, array $post_values) : bool
    {
        foreach (self::all() as $all) {
            if($all['period'] == $id) {
                foreach ($all['unit'] as $unit) {

                    if(isset($post_values[$unit['id']])) {
                        if($unit['id'] == "TIME") {

                            $x = LayDate::is_valid($post_values[$unit['id']]);

                            if(!$x)
                                return false;

                            continue;
                        }

                        $values = explode(",", $post_values[$unit['id']]);

                        if(LayArray::any($values, fn($v) => !in_array($v, $unit['values']))) {
                            return false;
                        }

                        return true;
                    }
                }
            }

        }
        return false;
    }

    /**
     * @param array{
     *     period: string,
     *     unit: array<int, array{
     *      id: string,
     *      values: array<int, string>
     *     }>,
     * } $frequency
     * @return bool
     */
    public static function is_now(array $frequency) : bool
    {
        $date = "";
        $positive_count = 0;
        $unit_total = 0;

        foreach ($frequency['unit'] as $unit) {
            $id = $unit['id'];
            $unit_total++;

            foreach ($unit['values'] as $value) {
                if($id == "TIME") {
                    if(LayDate::expired("$value")) {
                        $positive_count += 1;
                        break;
                    }
                }

                if($id == "DAYS") {
                    if(strtolower(LayDate::date(format: "D")) == $value) {
                        $positive_count += 1;
                        break;
                    }
                }

                if($id == "DAYS_NUM") {
                    if(LayDate::date(format: "j") == $value) {
                        $positive_count += 1;
                        break;
                    }
                }

                if($id == "MONTH") {
                    if(strtolower(LayDate::date(format: "M")) == $value) {
                        $positive_count += 1;
                        break;
                    }
                }

            }
        }

        return $unit_total == $positive_count;
    }

    public static function struct(array $keys, array $post_values) : array
    {
        $out = [];

        foreach ($keys as $i => $key) {
            $out[$i]['period'] = $key;

            foreach ($post_values[$i] as $k => $value) {
                $out[$i]['unit'][] = [
                    "id" => $k,
                    "values" => explode(",", $value)
                ];
            }

        }

        return $out;
    }

    public static function all() : array
    {
        return [
            self::by_hour(),
            self::by_day(),
            self::by_day_num(),
            self::by_month_day(),
            self::by_month_day_num(),
        ];
    }

    public static function by_hour() : array
    {
        return [
            "period" => "BY_HOUR",
            "unit" => [self::unit_time()]
        ];
    }

    public static function by_day() : array
    {
        return [
            "period" => "BY_DAY",
            "unit" => [self::unit_days(), self::unit_time()]
        ];
    }

    public static function by_day_num() : array
    {
        return [
            "period" => "BY_DAY_NUM",
            "unit" => [self::unit_days_num(), self::unit_time()]
        ];
    }

    public static function by_month_day_num() : array
    {
        return [
            "period" => "BY_MONTH_DAY_NUM",
            "unit" => [self::unit_month(), self::unit_days_num(), self::unit_time()]
        ];
    }

    public static function by_month_day() : array
    {
        return [
            "period" => "BY_MONTH_DAY",
            "unit" => [self::unit_month(), self::unit_days(), self::unit_time()]
        ];
    }

}