<?php

namespace Elevator\AiAgent;

use BrickLayer\Lay\Core\Api\Enums\ApiReturnType;
use Elevator\AiAgent\Controller\ElevatorAgentActions;
use Elevator\AiAgent\Controller\ElevatorAgentActivityController;
use Elevator\AiAgent\Controller\ElevatorAgentController;
use Elevator\AiAgent\Controller\ElevatorAgentWorkflowController;
use Elevator\AiAgent\Utils\FrequencyHandler;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Enums\PermissionAccessType;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorAgentController $agent;
    protected static ElevatorAgentWorkflowController $workflow;
    protected static ElevatorAgentActions $actions;
    protected static ElevatorAgentActivityController $activity;

    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix . "/ai");
    }

    protected function pre_hook() : void
    {
        $foundation = self::foundation();

        self::$agent = $foundation->ai_agent();
        self::$workflow = $foundation->ai_workflow();
        self::$actions = $foundation->ai_action();
        self::$activity = $foundation->ai_activity();
    }

    protected function hooks(): void
    {
        $this->primary_prefix();

        $this->elevator_middleware()->is_auth();

        $this->get("auth/list")
            ->middleware($this->elevator_middleware()->is_permitted(static::$agent))
            ->bind(fn() => static::$agent->auth_list());

        $this->group("actions", function () {
            $this->post("blog/gen")
                ->middleware($this->elevator_middleware()->is_permitted(static::$actions, PermissionAccessType::CREATE))
                ->bind(fn() => static::$actions->generate_blog_post());

            $this->post("blog-calendar/gen")->name("pre-stream-request")
                ->middleware($this->elevator_middleware()->is_permitted(static::$actions, PermissionAccessType::CREATE))
                ->bind(fn() => static::$actions->generate_post_calendar(true));

            $this->get("blog-calendar/gen", ApiReturnType::STREAM)->name("streamed-request")
                ->middleware($this->elevator_middleware()->is_permitted(static::$actions, PermissionAccessType::CREATE))
                ->bind(fn() => static::$actions->generate_post_calendar(false));

        });

        $this->group("agent", function () {
            $this->get("list/{page}")
                ->bind(fn($page) => static::$agent->available_agents((int) $page));

            $this->post("new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::CREATE))
                ->bind(fn() => static::$agent->new_agent());

            $this->post("update-perm/{id}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::EDIT))
                ->bind(fn($id) => static::$agent->update_permissions($id));

            $this->post("update-name/{id}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::EDIT))
                ->bind(fn($id) => static::$agent->update_name($id));

            $this->post("update-credential/{id}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::EDIT))
                ->bind(fn($id) => static::$agent->update_credential($id));

            $this->post("update-personality/{id}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::EDIT))
                ->bind(fn($id) => static::$agent->update_personality($id));

            $this->post("delete")
                ->middleware($this->elevator_middleware()->is_permitted(static::$agent, PermissionAccessType::DELETE))
                ->bind(fn() => static::$agent->delete());



            $this->get("logs")
                ->name("current-month-logs")
                ->middleware($this->elevator_middleware()->is_permitted(static::$activity))
                ->bind(fn() => static::$activity->list());

            $this->post("logs")
                ->name("specific-range-logs")
                ->middleware($this->elevator_middleware()->is_permitted(static::$activity))
                ->bind(fn() => static::$activity->list());
        });

        $this->group("workflow", function () {
            $this->get("all/{page}")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow))
                ->bind(fn($page) => static::$workflow->all_flows((int) $page));

            $this->get("predefined")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::CREATE))
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::EDIT))
                ->bind(fn() => static::$workflow->all_templates());

            $this->get("frequency")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::CREATE))
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::EDIT))
                ->bind(fn() => FrequencyHandler::all());

            $this->post("new")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::CREATE))
                ->bind(fn() => static::$workflow->new_flow());

            $this->post("edit")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::EDIT))
                ->bind(fn() => static::$workflow->edit_flow());

            $this->post("change-status/resume")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::EDIT))
                ->bind(fn() => static::$workflow->activate_workflow());

            $this->post("change-status/pause")
                ->middleware($this->elevator_middleware()->is_permitted(static::$workflow, PermissionAccessType::EDIT))
                ->bind(fn() => static::$workflow->deactivate_workflow());
        });

    }
}