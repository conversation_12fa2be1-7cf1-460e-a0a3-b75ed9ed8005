<?php

namespace Elevator\AiAgent\Controller;

use BrickLayer\Lay\Core\Api\Enums\ApiStatus;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;

use BrickLayer\Lay\Orm\SQL;
use Elevator\AiAgent\Enums\LLM;
use Elevator\AiAgent\Model\ElevatorAgentModel;
use Elevator\AiAgent\Providers\AbstractProvider;
use Elevator\AiAgent\Providers\Gemini;
use Elevator\AiAgent\Requests\NewAgentRequest;
use Elevator\AiAgent\Requests\UpdateAgentCredentialRequest;
use Elevator\AiAgent\Requests\UpdateAgentNameRequest;
use Elevator\AiAgent\Requests\UpdateAgentPermissionRequest;
use Elevator\AiAgent\Requests\UpdateAgentPersonalityRequest;
use Elevator\AiAgent\Resource\AgentResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorAgentController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::AiAgents;
    }

    public function model(): ElevatorAgentModel
    {
        return new ElevatorAgentModel();
    }

    public function auth_list() : array
    {
        return static::foundation()->user_auth()->model()->ai_users();
    }

    public function available_agents(int $page = 1, int $limit = 100) : array
    {
        return AgentResource::collect($this->model()->all($page, $limit));
    }

    public function is_valid(string $agent_id) : bool
    {
        return $this->model()->fill($agent_id)->exists();
    }

    public final function set_llm(?LLM $llm = LLM::GEMINI, array $args = []) : AbstractProvider
    {
        $api = $args['api'] ?? null;
        $timeout = $args['timeout'] ?? null;
        $buffer = $args['buffer'] ?? false;

        $args = [$api, $timeout, $buffer];

//        if($llm == LLM::GEMINI)
        return new Gemini(...$args);
    }

    /**
     * @param string $profile_id
     * @return array{
     *     name: string,
     *     slug: string,
     *     about: string,
     * }
     */
    public final function as_author(string $profile_id) : array
    {
        $me = $this->model()->fill($profile_id);

        return [
            "id" => $me->id,
            "name" => $me->name,
            "slug" => $me->slug,
            "about" => $me->about,
        ];
    }

    /**
     * @param string $agent_id
     * @param RequestHelper $request
     * @return array{
     *     code: int,
     *     message: string,
     *     data: array{
     *         agent: ElevatorAgentModel
     *     }
     * }
     */
    private function update_data(string $agent_id, RequestHelper $request) : array
    {
        if($request->error)
            return self::res_warning($request->error);

        $agent = $this->model()->fill($agent_id);

        if($agent->is_empty())
            return self::res_warning("Agent is invalid, refresh the page to try again");

        $updated = $agent->edit_self($request);

        if(!$updated)
            return self::res_warning("Could not update agent's data at the moment, please try again later");

        return self::res_success("Agent data updated successfully", [ "agent" => $agent ]);
    }

    public function update_permissions(string $agent_id) : array
    {
        $request = new UpdateAgentPermissionRequest();

        $res = $this->update_data($agent_id, $request);

        if(!ApiStatus::is_ok($res['code']))
            return $res;

        $agent = $res['data']['agent'];

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Agent [$agent->id] with name [$agent->name] permissions updated successfully"
        );

        return self::res_success("Agent's permissions updated successfully");
    }

    public function update_name(string $agent_id) : array
    {
        $request = new UpdateAgentNameRequest();

        $res = $this->update_data($agent_id, $request);

        if(!ApiStatus::is_ok($res['code']))
            return $res;

        $agent = $res['data']['agent'];

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Agent [$agent->id] name updated successfully from $request->name to $agent->name"
        );

        return self::res_success("Agent's name updated successfully");
    }

    private function upsert_credential(NewAgentRequest &$request) : ?array
    {
        $auth_id = $request->auth_id;

        if($request->credential_new) {
            $auth = static::foundation()->user_auth()->register_ai_admin($request->auth_id);

            if (!ApiStatus::is_ok($auth['code'])) return $auth;

            $auth_id = $auth['data']['auth_id'];
        }

        $request->unset("credential_new");
        $request->update("auth_id", $auth_id);

        return null;
    }

    public function update_credential(string $agent_id) : array
    {
        return SQL::scoped_transaction(function () use ($agent_id) {
            $request = new UpdateAgentCredentialRequest();

            if($request->error)
                return self::res_warning($request->error);

            $auth = $this->upsert_credential($request);

            if($auth) return $auth;

            $res = $this->update_data($agent_id, $request);

            if(!ApiStatus::is_ok($res['code']))
                return $res;

            $agent = $res['data']['agent'];

            $this->activity_log(
                LogActivityTypes::UPDATE,
                "Agent [$agent->id] credential was changed successfully from $agent->auth_id to $request->auth_id"
            );

            return self::res_success("Agent's credential updated successfully");
        })['data'];
    }

    public function update_personality(string $agent_id) : array
    {
        $request = new UpdateAgentPersonalityRequest();

        if($request->error)
            return self::res_warning($request->error);

        $agent = $this->model()->fill($agent_id);

        if($agent->is_empty())
            return self::res_warning("Agent is invalid, refresh the page to try again");

        $request->new_key("dataset", json_encode(
            array_merge($agent->dataset, [ "personality" => $request->personality ])
        ));

        $request->unset("personality");

        $updated = $agent->edit_self($request);

        if(!$updated)
            return self::res_warning("Could not update agent's data at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Agent [$agent->id] personality updated successfully"
        );

        return self::res_success("Agent's personality updated successfully");
    }

    public function new_agent() : array
    {
        $request = new NewAgentRequest();

        if($request->error)
            return self::res_warning($request->error);

        $agent = $this->model();

        if($agent->is_duplicate($request))
            return self::res_warning("Agent exists already, please refresh the page and try again");

        return SQL::scoped_transaction(function () use ($agent, $request) {
            $auth = $this->upsert_credential($request);

            if($auth) return $auth;

            $agent->add($request);

            if($agent->is_empty())
                return self::res_warning("Could not create agent at the moment, please try again later");

            $this->activity_log(
                LogActivityTypes::CREATE,
                "New AI Agent created [$agent->id]; name [$agent->name]"
            );

            return self::res_success("Agent $request->name created successfully");
        })['data'];
    }

    public function delete(?string $id = null) : array
    {
        $id ??= self::request(false)->id;

        if(!$id)
            return self::res_warning("An invalid Agent was received, please refresh and try again");

        $agent = $this->model()->fill(self::clean($id, strict: false));

        if($agent->is_empty())
            return self::res_warning("Agent is not valid, it may have been terminated already, please refresh and try again");

        $deleted = $agent->delete_self();

        if(!$deleted)
            return self::res_warning("Agent failed to terminate, it may have been already, please refresh and try again");

        $this->activity_log(
            LogActivityTypes::DELETE,
            "Agent with id [$agent->id] and name [$agent->name] deleted successfully"
        );

        return self::res_success("Agent [$agent->name] deleted successfully");
    }
}