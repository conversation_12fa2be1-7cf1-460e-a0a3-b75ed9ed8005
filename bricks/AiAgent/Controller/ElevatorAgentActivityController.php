<?php
declare(strict_types=1);

namespace Elevator\AiAgent\Controller;

use Elevator\AiAgent\Model\ElevatorAgentActivityModel;
use Elevator\AiAgent\Model\ElevatorAgentModel;
use Elevator\User\Controller\ElevatorActivityLogController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\PermissionInterface;

class ElevatorAgentActivityController extends ElevatorActivityLogController
{
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::AiAgentServiceWorker;
    }

    public function model(): ElevatorAgentActivityModel
    {
        return new ElevatorAgentActivityModel();
    }
}
