<?php

namespace Elevator\AiAgent\Controller;

use Brick<PERSON>ayer\Lay\Orm\SQL;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\AiAgent\Model\ElevatorAgentWorkflowModel;
use Elevator\AiAgent\Requests\EditWorkflowRequest;
use Elevator\AiAgent\Requests\NewWorkflowRequest;
use Elevator\AiAgent\Resource\AgentWorkflowResource;
use Elevator\AiAgent\Workflows\PostCalendarGenerator;
use Elevator\AiAgent\Workflows\PostCreator;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorAgentWorkflowController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::AiAgentWorkflow;
    }

    public function model(): ElevatorAgentWorkflowModel
    {
        return new ElevatorAgentWorkflowModel();
    }

    public function all_templates() : array
    {
        return [
            PostCalendarGenerator::info("id") => PostCalendarGenerator::info(),
            PostCreator::info("id") => PostCreator::info()
        ];
    }

    public function is_template_valid(string $id) : bool
    {
        return isset($this->all_templates()[$id]);
    }

    public function get_template(string $id) : array
    {
        return $this->all_templates()[$id];
    }

    public function execute(AgentWorkflowResource $wkf) : void
    {
        $class = $this->all_templates()[$wkf->templateId]['class'];

        static::foundation()->user_auth()->ai_login($wkf->agentId);

        new $class($wkf->agentId);
    }

    /**
     * Execute a real workflow and kill the process
     * @param string $wkf_id
     * @return void
     */
    public function test(string $wkf_id) : void
    {
        $wkf = $this->model()->fill($wkf_id);

        if($wkf->is_empty()) {
            echo "Workflow: $wkf_id does not exist!";
            return;
        }

        static::foundation()->user_session()::update_session('profile_id', $wkf->agent);
        $this->execute(new AgentWorkflowResource($wkf));

        die;
    }

    protected function start_service_worker() : void
    {
        static::foundation()->ai_service_worker()->start_service();
    }

    protected function stop_service_worker() : void
    {
        if(!empty($this->model()->active_flows(10))) return;

        static::foundation()->ai_service_worker()->stop_service();
    }

    public function new_flow() : array
    {
        $request = new NewWorkflowRequest();

        if($request->error)
            return self::res_warning($request->error);

        $workflow = $this->model();

        if($workflow->is_duplicate($request))
            return self::res_warning("Workflow [$request->name] exists already!");

        $workflow->add($request);

        if($workflow->is_empty())
            return self::res_warning("Failed to create new workflow");

        $this->activity_log(
            LogActivityTypes::CREATE,
            "New workflow with ID [$workflow->id] and name [$workflow->name] created successfully"
        );

        $this->start_service_worker();

        return self::res_success("Workflow [$workflow->name] created successfully");

    }

    public function edit_flow() : array
    {
        $request = new EditWorkflowRequest();

        if($request->error)
            return self::res_warning($request->error);

        $workflow = $this->model()->fill($request->id);

        if($workflow->is_empty())
            return self::res_warning("An invalid workflow was received");

        $edited = $workflow->edit_self($request);

        if(!$edited)
            return self::res_warning("Could not edit workflow at the moment, please try again later");

        $this->start_service_worker();

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Workflow with ID [$workflow->id] and name [$workflow->name] was updated successfully"
        );

        return self::res_success("Workflow [$workflow->name] updated successfully");
    }

    protected function change_status(bool $activate, ?string $id = null) : array
    {
        $id ??= self::request(false)->id;

        self::cleanse($id, strict: false);

        if(!$id)
            return self::res_warning("Invalid workflow received!");

        $workflow = $this->model()->fill($id);

        if($workflow->is_empty())
            return self::res_warning("Workflow is not valid");

        $status = $activate ? WorkflowStatus::ACTIVE->name : WorkflowStatus::PAUSED->name;

        $updated = $workflow->edit_self([
            "status" => $status
        ]);

        if(!$updated)
            return self::res_warning("Could not update workflow status to [$status] at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Workflow with id [$workflow->id] was updated successfully with status changed to [$status]"
        );

        $activate ? $this->start_service_worker() : $this->stop_service_worker();

        return self::res_success("Workflow status updated to [$status] successfully");
    }

    public function activate_workflow() : array
    {
        return $this->change_status(true);
    }

    public function deactivate_workflow() : array
    {
        return $this->change_status(false);
    }

    public function all_flows(int $page = 1, int $limit = 100) : array
    {
        return AgentWorkflowResource::collect(
            $this->model()
                ->pre_run(fn(SQL $db) => $db->sort("updated_at", "desc"))
                ->all($page, $limit)
        );
    }

    /**
     * @param int $limit
     * @return array<int, AgentWorkflowResource>
     */
    public function get_active_workflows(int $limit = 20) : array
    {
        return AgentWorkflowResource::collect(
            $this->model()->active_flows($limit),
            as_array: false
        );
    }

}