<?php

namespace Elevator\AiAgent\Controller;

use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\AiAgent\Model\ElevatorAgentServiceWorkerModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorAgentServiceWorkerController extends ElevatorController implements HasPermission, HasLog
{
    const JOB_SCRIPT = __DIR__ . "/../../../workers/workflow-processor.php";

    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::AiAgentServiceWorker;
    }

    public function model(): ElevatorAgentServiceWorkerModel
    {
        return new ElevatorAgentServiceWorkerModel();
    }

    public function init_worker() : ElevatorAgentServiceWorkerModel
    {
        return $this->model()->by_script(self::JOB_SCRIPT);
    }

    public function start_service() : void
    {
        $worker = $this->init_worker();

        if($worker->is_empty())
            $worker->start_work(self::JOB_SCRIPT, [
                "type" => "workflow-processor"
            ]);

        if($worker->status == WorkflowStatus::PAUSED)
            $worker->resume_work();
    }

    public function stop_service() : void
    {
        $worker = $this->model()->by_script(self::JOB_SCRIPT);

        if($worker->exists())
            $worker->stop_work();
    }

    public function processing(ElevatorAgentServiceWorkerModel $model) : void
    {
        $model->start_running();
    }

    public function is_processing() : bool
    {
        return $this->model()->is_running();
    }

    public function done_processing(ElevatorAgentServiceWorkerModel $model) : void
    {
        $model->stop_running();
    }

}