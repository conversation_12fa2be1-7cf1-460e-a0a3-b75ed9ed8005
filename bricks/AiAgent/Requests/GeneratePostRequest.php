<?php

namespace Elevator\AiAgent\Requests;

use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Libs\String\Enum\EscapeType;
use Elevator\AiAgent\Controller\ElevatorAgentActions;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string $agent
 * @property string $ai_title
 * @property string $ai_category
 * @property string|null $keywords
 * @property string|null $prompt
 */
class GeneratePostRequest extends RequestHelper
{
    protected function rules(): void
    {
        $foundation = ElevatorFoundation::new();

        $this->vcm([ "field" => "agent", "must_validate" => fn($v) => $foundation->ai_agent()->is_valid($v) ]);
        $this->vcm([ "field" => "ai_title", ]);
        $this->vcm([ "field" => "ai_category", "must_validate" => fn($v) => $foundation->post_category()->model()->fill($v)->exists() ]);
        $this->vcm([ "field" => "keywords", 'required' => false, "clean" => [
            "escape" => EscapeType::STRIP_TRIM
        ] ]);
        $this->vcm([ "field" => "prompt", 'required' => false ]);
    }
}