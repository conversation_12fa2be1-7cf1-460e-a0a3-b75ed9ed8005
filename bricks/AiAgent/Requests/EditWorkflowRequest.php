<?php

namespace Elevator\AiAgent\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\AiAgent\Controller\ElevatorAgentWorkflowController;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\AiAgent\Utils\FrequencyHandler;

/**
 * @property string id
 */
class EditWorkflowRequest extends NewWorkflowRequest
{
    protected function rules(): void
    {
        $this->vcm([ "field" => "id" ]);
        parent::rules();
    }

}
