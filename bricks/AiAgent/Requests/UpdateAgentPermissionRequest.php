<?php

namespace Elevator\AiAgent\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Bricks\SystemUser\Model\UserRole;
use Elevator\Utils\PermissionManager;

/**
 * @property array $permissions
 */
class UpdateAgentPermissionRequest extends RequestHelper
{

    protected function rules(): void
    {
        $this->vcm([
            "field" => "role",
            "must_validate" => fn($v) => (new UserRole($v))->exists()
        ]);

        $this->vcm([
            "field" => "permissions[]",
            "alias" => "x_permissions",
            "json_encode" => false,
            "required" => false,
            "after_clean" => fn($v) => json_encode(PermissionManager::unique_access($v))
        ]);

    }

    protected function post_validate(array $data): array
    {
        $data['x_permissions'] ??= null;

        return $data;
    }

}
