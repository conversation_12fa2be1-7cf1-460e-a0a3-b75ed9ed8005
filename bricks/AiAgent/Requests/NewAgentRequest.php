<?php

namespace Elevator\AiAgent\Requests;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\ElevatorFoundation;
use Elevator\Utils\PermissionManager;

/**
 * @property string $name
 * @property string $auth_id
 * @property string|null $credential_new
 * @property string $personality
 * @property array{
 *     personality: string,
 * } $dataset
 * @property array $x_permissions
 */
class NewAgentRequest extends RequestHelper
{
    protected function rules(): void
    {
        $foundation = ElevatorFoundation::new();

        $this->vcm([ "field" => "credential_new", 'required' => false ]);
        $this->vcm([ "field" => "credential", "alias" => "auth_id", "must_validate" => [
            "fun_str" => function ($v) use ($foundation) {
                $new = self::vcm_data()['credential_new'] ?? null;

                if($new || $foundation->user_auth()->ai_admin_is_valid($v)) return null;

                return "An invalid credential was received";
            }
        ]]);
        $this->vcm([ "field" => "name", ]);
        $this->vcm([
            "field" => "role",
            "is_uuid" => true,
            "must_validate" => fn($v) => $foundation->user_role()->model()->fill($v)->exists()
        ]);
        $this->vcm([
            "field" => "permissions[]",
            "alias" => "x_permissions",
            "json_encode" => false,
            "after_clean" => fn($v) => json_encode(PermissionManager::unique_access($v)),
            "required" => false
        ]);

        // Attributes column. Remember to unset them after combining them below
        $this->vcm([ "field" => "personality", ]);
    }

    protected function post_validate(array $data): array
    {
        if(isset($data['personality'])) {
            $data["dataset"] = json_encode([
                "personality" => $data['personality'],
            ]);

            unset($data["personality"]);
        }

        return $data;
    }
}
