<?php

namespace Elevator\AiAgent\Requests;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\AiAgent\Controller\ElevatorAgentActions;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string agent
 * @property string month
 * @property int year
 * @property int posts_per_week
 * @property int posts_per_day
 * @property string days_of_week
 * @property bool consider_holiday
 */
class GeneratePostCalendarRequest extends RequestHelper
{
    protected function rules(): void
    {
        $foundation = ElevatorFoundation::new();

        $this->vcm([ "field" => "agent", "must_validate" => fn($v) => $foundation->ai_agent()->is_valid($v) ]);
        $this->vcm([
            "field" => "month",
            "must_contain" => [
                'January', 'February', 'March',
                'April', 'May', 'June', 'July',
                'August', 'September', 'October',
                'November', 'December',
            ]
        ]);
        $this->vcm([ "field" => "year", "is_num" => true ]);
        $this->vcm([ "field" => "posts_per_week", "is_num" => true ]);
        $this->vcm([ "field" => "posts_per_day", "is_num" => true ]);
        $this->vcm([
            "field" => "days_of_week",
            "must_validate" => [
                "fun_str" => function ($val) {
                    $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

                    foreach (explode(",", $val) as $v) {
                        $v = trim($v);

                        if(!in_array($v, $days))
                            return "'$v' is not valid. Days of Week must be any of these: " . implode(",", $days);
                    }

                    return null;
                }
            ],
        ]);
        $this->vcm([ "field" => "consider_holidays", "is_bool" => true ]);
    }
}