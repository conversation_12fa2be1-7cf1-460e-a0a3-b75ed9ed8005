<?php

namespace Elevator\AiAgent\Requests;

use Brick<PERSON>ayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\AiAgent\Utils\FrequencyHandler;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string $workflow_id
 * @property string $name
 * @property string $agent
 * @property string $status
 * @property array $frequency
 * @property array|null $dependency
 */
class NewWorkflowRequest extends RequestHelper
{
    protected function rules(): void
    {
        $foundation = ElevatorFoundation::new();
        $workflow = $foundation->ai_workflow();

        $this->vcm([
            "field" => "template",
            "must_validate" => fn($v) => $workflow->is_template_valid($v)
        ]);

        $this->vcm([ "field" => "name", "field_name" => "Workflow Name" ]);
        $this->vcm([ "field" => "agent", "must_validate" => fn($v) => $foundation->ai_agent()->model()->fill($v)->exists() ]);
        $this->vcm([ "field" => "status", "must_validate" => fn($v) => WorkflowStatus::is_enum($v), 'required' => false ]);
        $this->vcm([ "field" => "values", "json_encode" => false ]);
        $this->vcm([
            "field" => "frequency",
            "json_encode" => false,
            "must_validate" => fn ($key, $option) => FrequencyHandler::validate($key, $this->get("values")[$option['array_index']]),
            "return_struct" => fn ($keys) => json_encode(FrequencyHandler::struct($keys, $this->get("values"))),
        ]);

        $this->vcm([
            "field" => "dependency[]",
            "required" => false,
            "must_validate" => fn ($deps)  => $workflow->model()->all_valid($deps, "id")
        ]);
    }

    protected function post_validate(array $data): array
    {
        unset($data["values"]);
        $data['status'] ??= WorkflowStatus::ACTIVE->name;

        return $data;
    }

}
