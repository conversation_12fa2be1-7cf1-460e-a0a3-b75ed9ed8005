<?php

namespace Elevator\AiAgent\Requests;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\Utils\PermissionManager;

/**
 * @property string $name
 * @property string $description
 * @property array{
 *     api_key: string,
 *     instruction: string,
 * } $attributes
 * @property string $default_llm
 * @property array $permissions
 */
class NewBlogCalendar extends RequestHelper
{

    protected function rules(): void
    {
        $this->vcm([ "field" => "name", ]);
        $this->vcm([ "field" => "description", 'max_length' => 300, 'required' => false ]);
        $this->vcm([
            "field" => "permissions[]",
            "json_encode" => false,
            "after_clean" => fn($v) => json_encode(PermissionManager::unique_access($v))
        ]);
        $this->vcm([ "field" => "default_llm", "required" => false ]);

        // Attributes column. Remember to unset them after combining them below
        $this->vcm([ "field" => "personality", ]);
        $this->vcm([ "field" => "api_key", "required" => false ]);
    }

    protected function post_validate(array $data): array
    {
        $data["attributes"] = json_encode([
            "instruction" => $data['personality'],
            "api_key" => $data['api_key'] ?? null,
        ]);

        unset($data['personality'], $data['api_key']);

        return $data;
    }
}
