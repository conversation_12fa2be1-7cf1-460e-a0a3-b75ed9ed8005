<?php

namespace Elevator\AiAgent\Requests;


class UpdateAgentCredentialRequest extends NewAgentRequest
{

    protected function rules(): void
    {
        $this->vcm([ "field" => "credential_new", 'required' => false ]);
        $this->vcm([ "field" => "credential", "alias" => "auth_id", "must_validate" => [
            "fun_str" => function ($v) {
                $new = self::vcm_data()['credential_new'] ?? null;

                if($new || self::$controller->auth_ctrl()->ai_admin_is_valid($v)) return null;

                return "An invalid credential was received";
            }
        ]]);
    }

}
