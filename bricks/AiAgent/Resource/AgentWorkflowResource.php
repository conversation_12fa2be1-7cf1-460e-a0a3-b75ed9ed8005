<?php

namespace Elevator\AiAgent\Resource;

use <PERSON><PERSON><PERSON>er\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\AiAgent\Model\ElevatorAgentWorkflowModel;
use Elevator\Utils\ElevatorFoundation;

/**
 * @property string flowId
 * @property string name
 * @property string template
 * @property string templateId
 * @property string agent
 * @property string agentId
 * @property array frequency
 * @property string status
 * @property bool isActive
 * @property string dateCreated
 * @property string|null dateUpdated
 */
class AgentWorkflowResource extends ResourceHelper
{
    protected static ElevatorFoundation $foundation;

    protected function pre_schema(): void
    {
        self::$foundation = ElevatorFoundation::new();
    }

    protected function schema(object $data): array
    {
        $template = self::$foundation->ai_workflow()->get_template($data->template);

        /**
         * @var ElevatorAgentWorkflowModel $data
         */
        return [
            "flowId" => $data->id,
            "name" => $data->name,
            "template" => $template['name'],
            "templateId" => $template['id'],
            "agent" => $data->agent_name,
            "agentId" => $data->agent,
            "frequency" => $data->frequency,
            "status" => $data->status->name,
            "isActive" => $data->status == WorkflowStatus::ACTIVE,
            "dateCreated" => LayDate::date($data->created_at, format_index: 3),
            "dateUpdated" => $data->updated_at ? LayDate::date($data->updated_at, format_index: 3) : null,
        ];
    }
}