<?php

namespace Elevator\AiAgent\Resource;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Libs\LayDate;
use BrickL<PERSON>er\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\AiAgent\Model\ElevatorAgentModel;

/**
 * @property string $agentId
 * @property string $name
 * @property string $auth
 * @property string $authId
 * @property string $role
 * @property string $roleId
 * @property array $permissions
 * @property array $xPermissions
 * @property string $personality
 * @property string $date
 */
class AgentResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorAgentModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "auth" => $data->agent_credential,
            "authId" => $data->auth_id,
            'role' => $data->role_name,
            'roleId' => $data->role,
            'permissions' => $data->permissions,
            "xPermissions" => $data->x_permissions,
            "personality" => $data->personality(),
            "date" => LayDate::date($data->created_at, format_index: 3),
            "dateUpdated" => $data->updated_at ? LayDate::date($data->updated_at, format_index: 3) : null,
        ];
    }
}