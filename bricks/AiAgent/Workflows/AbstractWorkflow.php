<?php

namespace Elevator\AiAgent\Workflows;

use Brick<PERSON>ayer\Lay\Libs\LayFn;
use Elevator\AiAgent\Controller\ElevatorAgentController;
use Elevator\AiAgent\Model\ElevatorAgentModel;
use Elevator\AiAgent\Providers\AbstractProvider;
use Elevator\AiAgent\Resource\AgentWorkflowResource;
use Elevator\Utils\ElevatorFoundation;

abstract class AbstractWorkflow
{
    protected ElevatorFoundation $foundation;
    protected ElevatorAgentModel $agent_model;
    protected AbstractProvider $llm;
    protected ElevatorAgentController $agent_ctrl;

    /**
     * @param string $key A key to directly access a value of the array object.
     * `"*"` means return the entire array
     * @return string|array{
     *     id: string,
     *     name: string,
     *     about: string,
     *     class: string, // self::class
     * }
     */
    abstract public static function info(string $key = "*") : array|string;

    /**
     * Main execution function for workflow
     * @param array $opts Options needed by the function
     * @return void
     */
    abstract protected function main(array $opts) : void;

    public function __construct(protected string $agent_profile_id, array $args = [])
    {
        $this->foundation = ElevatorFoundation::new();

        $this->agent_ctrl = $this->foundation->ai_agent();
        $this->agent_model = $this->agent_ctrl->model()->fill($this->agent_profile_id);

        $this->llm = $this->agent_ctrl->set_llm($this->agent_model->default_llm(), [
            "api" => $this->agent_model->api_key() ?? LayFn::env("GEMINI_API_KEY"),
        ]);

        $this->main($args);
    }
}