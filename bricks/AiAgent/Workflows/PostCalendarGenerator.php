<?php

namespace Elevator\AiAgent\Workflows;

use <PERSON><PERSON><PERSON>er\Lay\Core\Api\Enums\ApiStatus;
use <PERSON><PERSON>ayer\Lay\Libs\LayArray;
use BrickLayer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Primitives\Enums\LayLoop;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use BrickLayer\Lay\Libs\ServerEvents\Events;
use Elevator\AiAgent\Enums\LLMResponseType;
use Elevator\Blog\Enums\PostConversionGoal;
use Elevator\Blog\Enums\PostKpis;
use Elevator\Blog\Enums\PostSearchIntent;
use Elevator\Blog\Enums\PostSocialChannels;
use Elevator\Blog\Enums\PostTypes;
use Elevator\Utils\Enums\LogActivityTypes;

class PostCalendarGenerator extends AbstractWorkflow
{
    use ControllerHelper;

    public static function info(string $key = "*") : array|string
    {
        $data = [
            "id" => "wkf-post-cal-gen",
            "name" => "Post Calendar Generator",
            "about" => "This workflow generates a blog post calendar according to your specified frequency for a month. 
            This calendar is tailored to your brand identity",
            "class" => self::class
        ];

        if($key == "*") return $data;

        return $data[$key];
    }

    /**
     * @param array{
     *     brand_name: string,
     *     brand_audience: string,
     *     brand_personality: string,
     *     month: string, // August
     *     year: int, // 2025
     *     posts_per_week: int, // August 2025
     *     posts_per_day: int, // August 2025
     *     days_of_week: string, // Weekdays, Weekends, Strategic, Random, (Mon, Tue, Wed)
     *     consider_holidays: bool,
     * } $opts
     */
    public function main(array $opts) : void
    {
        $llm = $this->llm;

        $sse = $llm->sse_client();

        $month = ($opts['month'] ?? date("F")) . " " . ($opts['year'] ?? date("Y"));
        $post_per_week = $opts['posts_per_week'] ?? 5; // 5
        $post_per_day = $opts['posts_per_day'] ?? 1; // 1
        $days_of_week = $opts['days_of_week'] ?? "Strategic"; // Weekdays, Weekends, Strategic, Random, Mon, Tue, Wed
        $consider_holidays = $opts['consider_holidays'] ?? true;

        $gen_id = $month . "-" . $post_per_week . "-" . $post_per_day . "-" . $days_of_week . "-holi-" . $consider_holidays;

        $sse->set_event_id($gen_id);

        if($sse->is_duplicate()) {
            $sse->event("chunked", [
                'message' => "This exact calendar has been generated already, please try again later"
            ]);
            $sse->complete();
            return;
        }

        $output_cache = $llm->parse_gen($gen_id);

        if($output_cache) {
            $this->insert_to_table($month, $output_cache);
            $sse->event("chunked", [
                'message' => "Updated calendar from cache"
            ]);
            $sse->complete();
            return;
        }

        $categories = $this->foundation->post_category()->list(1, 500);

        if(empty($categories)) {
            $sse->error("Post Categories are empty. You need to populate your post categories to generate a post calendar");
            return;
        }

        $categories = implode("\n", LayArray::map($categories, fn($v) => "- id: " . $v['id'] . "; name: " . $v['name']));
        $goals = implode("\n", LayArray::map(PostConversionGoal::cases(), fn($v) => "- " . $v->name));
        $kpis = implode("\n", LayArray::map(PostKpis::cases(), fn($v) => "- " . $v->name));
        $intents = implode("\n", LayArray::map(PostSearchIntent::cases(), fn($v) => "- " . $v->name));
        $channels = implode("\n", LayArray::map(PostSocialChannels::cases(), fn($v) => "- " . $v->name));
        $types = implode("\n", LayArray::map(PostTypes::cases(), fn($v) => "- " . $v->name));

        $brand_ctrl = $this->foundation->brand_ctrl()->current_brand();

        $brand = $opts['brand_name'] ?? $brand_ctrl->name;
        $audience = $opts['brand_audience'] ?? $brand_ctrl->audience;
        $personality = $opts['brand_personality'] ?? $brand_ctrl->personality;

        if($post_per_week)
            $post_per_week = "Total posts per week: $post_per_week";

        if($post_per_day)
            $post_per_day = "Total posts per day: $post_per_day";

        if($days_of_week)
            $days_of_week = "Days of the week posts should be scheduled for: $days_of_week";

        if($consider_holidays)
            $consider_holidays = "Consider incorporating holidays into the calendar in situations where the post day falls on a holiday";

        $llm->temperature(0.9)->instruction(
            "You are a brand called: $brand
            This is your brand personality: $personality
            This is your target audience: $audience"
        );

        $total_weeks = LayDate::month_total_weeks($month);
        $start_week = 1;
        $end_week = 2;
        $buffer_file = "";

        $sse->event_loop(function (Events $events) use (
            &$start_week, &$end_week, &$buffer_file, $total_weeks,
            $llm, $gen_id, $month, $post_per_day,$post_per_week,$days_of_week,
            $consider_holidays, $categories, $goals, $kpis, $intents, $channels, $types
        ) : LayLoop {
            $buffer_file = $llm->gen_id($gen_id, false)->autoclose(false)
                ->response_type(LLMResponseType::JSON)
                ->response_schema([
                    "post_title" => [
                        "type" => "STRING",
                        "description" => "An SEO-friendly compelling, click-worthy post title that must be unique; so don't repeat it under any circumstance!"
                    ],
                    "post_categories" => [
                        "type" => "ARRAY",
                        "items" => [
                            "type" => "STRING",
                        ],
                        "description" => "The IDs of post categories available on the platform. A post can have multiple categories. We want the exact IDs as provided here. Use the name attached to each ID as your guide when assigning an id"
                    ],
                    "post_summary" => [
                        "type" => "STRING",
                        "description" => "A brief summary of the post to be composed, which will serve as a guide to the author. It should not be more than 200 characters"
                    ],
                    "audience" => [
                        "type" => "STRING",
                        "description" => "The target audience the post will be written for",
                    ],
                    "goals" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "Post conversion goal. We have predefined goals, but you can add anyone we left out, but it must follow this format"
                    ],
                    "search_intents" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "Post search intention to consider when composing. We have predefined values, use only these values"
                    ],
                    "pry_keyword" => [
                        "type" => "STRING",
                        "description" => "The primary keyword the post should target",
                    ],
                    "sec_keywords" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "A list of secondary keywords the post may contain. Always consider the brand name and related keywords that will improve the brand's visibility here",
                    ],
                    "post_type" => [
                        "type" => "STRING",
                        "description" => "The post type. We have a predefined list, use any of them as a value",
                    ],
                    "publish_date" => [
                        "type" => "STRING",
                        "description" => "Day in the month the post should be published. Format is YYYY-MM-DD",
                    ],
                    "word_count" => [
                        "type" => "STRING",
                        "description" => "Total word count the author should consider when composing the post and it should be a number",
                    ],
                    "cta" => [
                        "type" => "STRING",
                        "description" => "A call-to-action the reader of the post should take. We only want one call-to-action",
                    ],
                    "assets_needed" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "A list of assets the author should consider when creating the post. This can be empty ",
                    ],
                    "social_channels" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "A recommended list of channels the post should be shared to for optimal traffic. We will send a predefined list, but you can suggest others we miss"
                    ],
                    "kpis" => [
                        "type" => "ARRAY",
                        "items" => ["type" => "STRING"],
                        "description" => "A list of Key Performance Indicators the author should consider as a measure for success or failure. We will supply you with a predefined list, use only these values"
                    ],

                ], "A list of posts for the various days of the month")
                ->required_fields([
                    "post_title",
                    "post_categories",
                    "post_summary",
                    "audience",
                    "goals",
                    "search_intents",
                    "pry_keyword",
                    "sec_keywords",
                    "post_type",
                    "publish_date",
                    "word_count",
                    "cta",
                    "assets_needed",
                    "social_channels",
                    "kpis",
                ])
                ->prompt(<<<EOD
                Generate a detailed blog post content calendar for the month of "$month".
                Since this content will be so much, I want you to start at week: $start_week and end at week: $end_week.
                
                Each post will have its own overarching category. I will provide you with a predefined list of
                values you should use where necessary. Then for other places, be creative, and remember to consider SEO
                and the compelling nature of the post.
                
                $post_per_day
                $post_per_week
                $days_of_week
                $consider_holidays
                
                Available Categories (use these exact IDs for 'post_categories'):
                $categories
                
                Available Goals (use these exact values for 'goals'):
                $goals
                
                Available KPIS (use these exact values for 'kpis'):
                $kpis
                
                Available Search Intents (use these exact values for 'search_intents'):
                $intents
                
                Available Social Channels (use these exact values for 'social_channels'):
                $channels
                
                Available Post Types (use these exact values for 'post_type'):
                $types
                
                Ensure a good mix of post types and topics throughout the month. The content should be highly engaging and valuable.
                Strictly adhere to the JSON schema provided.
                
                Make sure post_summary does not exceed 200 characters because it will break the application. This app is built to strictly adhere to best SEO practices.
                EOD);

            $events->event("chunked", [
                'message' => "Week $start_week - Week $end_week is Done"
            ]);

            if($end_week == $total_weeks) {
                $llm->close();
                return LayLoop::BREAK;
            }

            $start_week += 2;
            $start_week = $start_week > $total_weeks ? $total_weeks : $start_week;

            $end_week += 2;
            $end_week = $end_week > $total_weeks ? $total_weeks : $end_week;

            return LayLoop::FLOW;
        },
            false
        );

        $out = $this->insert_to_table($month, $llm->parse_file($buffer_file));

        if(ApiStatus::is_ok($out['code'])) $sse->complete();
        else $sse->error("Could not save to save calendar at the moment, please try again later");

    }

    protected function insert_to_table(string $month, array $rows) : array
    {
        $calendar = $this->foundation->post_calendar();
        $inserted = $calendar->model()->batch($rows);

        if(!$inserted)
            return self::res_warning("Could save generated records at the moment, please try again later");

        $this->foundation->ai_action()->activity_log(
            LogActivityTypes::CREATE,
            "Generated a content calendar for month: [$month]",
            created_by: $this->agent_model->auth_id
        );

        if(!$this->foundation->user_session()::get("is_ai_user", false))
            $calendar->activity_log(
                LogActivityTypes::CREATE,
                "New content calendar generated for month: [$month] via AiAgent. Check Agent Logs for more info",
            );

        return self::res_success("Content calendar generated successfully");
    }
}