<?php

namespace Elevator\AiAgent\Enums;


enum LLMSafetySettings
{
    case HARM_CATEGORY_HARASSMENT;
    case HARM_CATEGORY_HATE_SPEECH;
    case HARM_CATEGORY_SEXUALLY_EXPLICIT;
    case HARM_CATEGORY_DANGEROUS_CONTENT;

    /**
     * @param string $threshold
     * @return array{
     *     category: string,
     *     threshold: string,
     * }
     */
    private function struct(string $threshold) :array
    {
        return [
            "category" => $this->name,
            "threshold" => $threshold,
        ];
    }

    public function block_none() : array
    {
        return $this->struct("BLOCK_NONE");
    }

    public function block_low_above() : array
    {
        return $this->struct("BLOCK_LOW_AND_ABOVE");
    }

    public function block_medium_above() : array
    {
        return $this->struct("BLOCK_MEDIUM_AND_ABOVE");
    }

    public function block_high_above() : array
    {
        return $this->struct("BLOCK_HIGH_AND_ABOVE");
    }


    public function llm_default() : array
    {
        return $this->struct("HARM_BLOCK_THRESHOLD_UNSPECIFIED");
    }

}
