<?php
declare(strict_types=1);

namespace Elevator\AiAgent\Model;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use <PERSON><PERSON>ayer\Lay\Core\LayException;
use BrickLayer\Lay\Libs\Cron\LayCron;
use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use BrickLayer\Lay\Orm\SQL;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $name
 * @property string $path
 * @property WorkflowStatus $status
 * @property array $other
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorAgentServiceWorkerModel extends BaseModelHelper
{
    public static string $table = "elevator_ai_agent_service_workers";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema(): void
    {
        $this->cast("other", 'array', []);
        $this->cast("status", WorkflowStatus::class, null, fn($v) => WorkflowStatus::to_enum($v));
    }

    public function is_duplicate(array|RequestHelper $columns): bool
    {
        $columns = $this->req_2_array($columns);

        return $this
                ->pre_run(fn(SQL $db) => $db->and_where("client_id", $columns['client_id'] ?? $this->client_id()))
                ->count('name', $columns['name']) > 0;
    }

    public function make_path_relative(string $path) : string
    {
        return str_replace(LayConfig::server_data()->root, "", $path);
    }

    public function by_script(string $script_path) : static
    {
        return $this->get_by("path", $this->make_path_relative($script_path));
    }

    public function start_work(string $script, ?array $tag = null) : static
    {
        $script = $this->make_path_relative($script);

        $this->add([
            "path" => $script,
            "status" => WorkflowStatus::ACTIVE->name,
            "other" => json_encode($tag)
        ]);

        if($this->exists()) {
            $out = LayCron::new()
                ->job_id($this->id)
                ->every_minute(5)
                ->new_job($script);

            if (!$out['exec'])
                LayException::log($out['msg'], log_title: "AgentWorkerErr");
        }

        return $this;
    }

    public function resume_work(?array $tag = null) : static
    {
        $col = [
            "status" => WorkflowStatus::ACTIVE->name
        ];

        if($tag)
            $col['other'] = json_encode(array_merge($this->other, $tag));

        $this->edit_self($col);

        $out = LayCron::new()
            ->job_id($this->id)
            ->every_minute(5)
            ->new_job($this->path);

        if (!$out['exec'])
            LayException::log($out['msg'], log_title: "AgentWorkerErr");

        return $this;
    }

    public function stop_work() : static
    {
        $this->edit_self([
            "status" => WorkflowStatus::PAUSED->name
        ]);

        LayCron::new()->unset($this->id);

        return $this;
    }

    public function start_running() : void
    {
        $other = array_merge($this->other ?? [], [
            "exec_status" => "RUNNING"
        ]);

        $this->edit_self([
            "other" => json_encode($other)
        ]);
    }

    public function stop_running() : void
    {
        $other = array_merge($this->other ?? [], [
            "exec_status" => "IDLE"
        ]);

        $this->edit_self([
            "other" => json_encode($other)
        ]);
    }

    public function is_running() : bool
    {
        return @$this->other['exec_status'] == "RUNNING";
    }

}
