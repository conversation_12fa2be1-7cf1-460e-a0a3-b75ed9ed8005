<?php
declare(strict_types=1);

namespace Elevator\AiAgent\Model;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayException;
use <PERSON><PERSON><PERSON>er\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Brick<PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\AiAgent\Enums\LLM;
use Elevator\User\Model\ElevatorAuthModel;
use Elevator\User\Model\ElevatorProfileModel;
use Elevator\User\Model\ElevatorRoleModel;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $auth_id
 * @property string $name
 * @property array $x_permissions
 * @property string $role
 * @property string $about
 * @property string $slug
 * @property array $dataset
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint Properties
 * @property string role_name
 * @property array permissions
 * @property string agent_credential
 */
class ElevatorAgentModel extends BaseModelHelper
{
    public static string $table = "elevator_ai_agent_profile";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema(): void
    {
        $this->cast("x_permissions", "array", []);
        $this->cast("permissions", "array", []);
        $this->cast("dataset", 'array', []);
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->user_role()->model(), "role")
            ->use("name", "role_name")
            ->use("permissions",);

        $this->join(self::foundation()->user_auth()->model(),"auth_id")
            ->use("login_name", "agent_credential");

    }

    public function is_duplicate(array|RequestHelper $columns): bool
    {
        $columns = $this->req_2_array($columns);

        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $columns['client_id'] ?? $this->client_id()))
            ->count('name', $columns['name']) > 0;
    }

    public function by_auth_id(string $auth_id, ?string $client_id = null) : static
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $client_id ?? $this->client_id()))
            ->get_by("auth_id", $auth_id);
    }

    private function is_filled() : void
    {
        if($this->is_empty())
            LayException::throw("Trying to get agent's personality without initializing agent", "NoAgentId");
    }

    public function default_llm() : ?LLM
    {
        $this->is_filled();

        $x = $this->dataset['default_llm'] ?? null;

        if(!$x) return null;

        return LLM::to_enum($x);
    }

    public function personality() : ?string
    {
        $this->is_filled();

        return $this->dataset['personality'] ?? null;
    }

    public function api_key() : ?string
    {
        $this->is_filled();

        return $this->dataset['api_key'] ?? null;
    }

}
