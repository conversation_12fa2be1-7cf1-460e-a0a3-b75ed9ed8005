<?php
declare(strict_types=1);

namespace Elevator\AiAgent\Model;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayException;
use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\AiAgent\Enums\LLM;
use Elevator\AiAgent\Enums\WorkflowStatus;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string $template
 * @property string $name
 * @property string $agent
 * @property WorkflowStatus $status
 * @property array $frequency
 * @property array<string> $dependency
 * @property string $description
 * @property bool $one_time
 * @property array $other
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 *
 * // Joint properties
 * @property string agent_name
 */
class ElevatorAgentWorkflowModel extends BaseModelHelper
{
    public static string $table = "elevator_ai_agent_workflows";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema(): void
    {
        $this->cast("one_time", 'bool', false);
        $this->cast("dependency", 'array', []);
        $this->cast("other", 'array', []);
        $this->cast("frequency", 'array', []);
        $this->cast("status", WorkflowStatus::class, null, fn($v) => WorkflowStatus::to_enum($v));
    }

    protected function prefill() : void
    {
        $this->join(self::foundation()->ai_agent()->model(), "agent")
            ->use("name", "agent_name");

    }

    public function is_duplicate(array|RequestHelper $columns): bool
    {
        $columns = $this->req_2_array($columns);

        return $this
            ->pre_run(fn(SQL $db) => $db->and_where("client_id", $columns['client_id'] ?? $this->client_id()))
            ->count('name', $columns['name']) > 0;
    }

    public function active_flows(int $limit) : array
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->limit($limit))
            ->all_by_col("status", WorkflowStatus::ACTIVE->name);
    }
}
