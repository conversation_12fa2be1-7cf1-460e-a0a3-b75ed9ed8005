<?php

namespace Elevator\Country\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Country\Model\ElevatorStateModel;

/**
 * @property string id
 * @property string name
 * @property string capital
 */
class StateResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorStateModel $data
         */

        return [
            "id" => $data->id,
            "name" => $data->name,
            "capital" => $data->capital,
        ];
    }
}