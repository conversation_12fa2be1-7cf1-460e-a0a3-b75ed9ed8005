<?php

namespace Elevator\Country\Resource;

use BrickLayer\Lay\Libs\Primitives\Abstracts\ResourceHelper;
use Elevator\Country\Model\ElevatorCountryModel;

/**
 * @property string iso
 * @property string name
 * @property string currencyCode
 * @property string callingCode
 * @property string flag
 */
class CountryResource extends ResourceHelper
{
    protected function schema(object $data): array
    {
        /**
         * @var ElevatorCountryModel $data
         */

        return [
            "iso" => $data->iso3,
            "name" => $data->name,
            "callingCode" => $data->calling_code,
            "currencyCode" => $data->currency_code,
            "flag" => $data->flag,
        ];
    }
}