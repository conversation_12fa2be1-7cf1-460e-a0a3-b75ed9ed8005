<?php

namespace Elevator\Country\Controller;

use Elevator\Country\Model\ElevatorCountryModel;
use Elevator\Country\Resource\CountryResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorCountryController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Settings;
    }

    public function model(): ElevatorCountryModel
    {
        return new ElevatorCountryModel();
    }

    public function all_countries() : array
    {
        return CountryResource::collect($this->model()->all_sorted());
    }

    public function by_iso3(string $iso) : ElevatorCountryModel
    {
        return $this->model()->by_iso3(self::clean($iso));
    }
}