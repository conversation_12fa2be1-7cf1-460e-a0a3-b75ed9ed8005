<?php

namespace Elevator\Country\Controller;

use Elevator\Country\Model\ElevatorCountryModel;
use Elevator\Country\Model\ElevatorStateModel;
use Elevator\Country\Resource\CountryResource;
use Elevator\Country\Resource\StateResource;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorStateController extends ElevatorController implements HasPermission, HasLog
{
    use ActivityLog;

    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::Settings;
    }

    public function model(): ElevatorStateModel
    {
        return new ElevatorStateModel();
    }

    public function by_iso(string $iso) : array
    {
        return StateResource::collect($this->model()->all_by_country(self::clean($iso)));
    }

    public function by_name(string $name) : ElevatorStateModel
    {
        return $this->model()->by_name(self::clean($name));
    }

    public function like_name(string $name) : ElevatorStateModel
    {
        return $this->model()->by_name(self::clean($name), true);
    }

}