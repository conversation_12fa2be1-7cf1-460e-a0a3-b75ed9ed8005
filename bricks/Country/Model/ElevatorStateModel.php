<?php
declare(strict_types=1);

namespace Elevator\Country\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string country_iso3
 * @property string name
 * @property string capital
 * @property string zone
 * @property string slogan
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorStateModel extends BaseModelHelper
{
    public static string $table = "elevator_states";

    use CreatedBy;
    use HasFoundation;

    public function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name', 'country_iso3'],
            action: 'REPLACE'
        );
    }

    public function all_by_country(string $iso) : array
    {
        return $this->all_by_col("country_iso3", $iso);
    }

    public function by_name(string $name, bool $use_like = false) : self
    {
        if($use_like)
            return $this->get_by("name", "LIKE", "%$name%");

        return $this->get_by("name", $name);
    }
}
