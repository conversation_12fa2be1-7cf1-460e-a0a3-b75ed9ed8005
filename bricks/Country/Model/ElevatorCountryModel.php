<?php
declare(strict_types=1);

namespace Elevator\Country\Model;

use <PERSON><PERSON>ayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use <PERSON><PERSON><PERSON>er\Lay\Orm\SQL;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string name
 * @property string iso2
 * @property string iso3
 * @property string currency_code
 * @property string calling_code
 * @property string|null flag
 * @property string symbol
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 */
class ElevatorCountryModel extends BaseModelHelper
{
    public static string $table = "elevator_countries";

    use CreatedBy;
    use HasFoundation;

    public function resolve_conflict(SQL $db): void
    {
        $db->on_conflict(
            unique_columns: ['name'],
            action: 'REPLACE'
        );
    }

    public function by_iso3(string $id) : self
    {
        return $this->get_by("iso3", $id);
    }

    public function as_options() : array
    {
        return $this
            ->pre_run(fn(SQL $db) => $db->sort("name"))
            ->each(fn(self $country) => [
                "id" => $country->iso3,
                "name" => $country->name
            ])
            ->all(1, 500);
    }

    public function all_sorted() : array
    {
        return $this->pre_run(fn(SQL $db) => $db->sort("name"))->all(1, 500);
    }
}
