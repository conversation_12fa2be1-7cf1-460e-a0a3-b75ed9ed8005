<?php

declare(strict_types=1);

namespace Elevator\Utils;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Libs\LayArray;
use Elevator\Utils\Enums\PermissionAccessType;
use Elevator\Utils\Interface\PermissionInterface;

abstract class PermissionManager
{
    /**
     * Test if a user has permission to access a module
     *
     * @param PermissionInterface|null $permission_enum Current module id
     * @param PermissionAccessType|null $permission_access_type
     * @param string|array<string> $user_permissions User permission as non-associative array of strings or JSON string
     * @param bool $grant_all_access
     * @return array{
     *     grant: bool,
     *     access_type: PermissionAccessType|null,
     *     reason: string
     * }
     */
    public static function check_access(
        ?PermissionInterface  $permission_enum,
        ?PermissionAccessType $permission_access_type,
        array|string          $user_permissions,
        bool                  $grant_all_access = false
    ): array
    {
        if(!$grant_all_access and empty($user_permissions))
            return [
                "grant" => false,
                "access_type" => $permission_access_type,
                "reason" => "No permission to test!"
            ];

        if($grant_all_access)
            return [
                "grant" => true,
                "access_type" => $permission_access_type,
                "reason" => "Access was manually granted"
            ];

        if(is_null($permission_enum) || is_null($permission_access_type))
            return [
                "grant" => false,
                "access_type" => null,
                "reason" => "Module enum or permission access is null"
            ];

        $user_permissions = is_string($user_permissions) ? json_decode($user_permissions, true) : $user_permissions;

        $grant = LayArray::any($user_permissions, function($perm) use ($permission_enum, $permission_access_type) {
            $entry = explode(".", $perm, 2);
            $access = $entry[0];
            $module = $entry[1] ?? null;

            if ($permission_enum->name !== $module)
                return false;

            if(
                $access == PermissionAccessType::ALL->name ||
                $access == $permission_access_type->name
            )
                return true;

            return false;
        });

        if ($grant)
            return [
                "grant" => true,
                "access_type" => $permission_access_type,
                "reason" => "User has permission to access this module"
            ];

        return [
            "grant" => false,
            "access_type" => $permission_access_type,
            "reason" => "User does not have permission to access this module"
        ];
    }

    /**
     * List all the modules of a Module Enum class
     *
     * @param string|array{
     *      main: string,
     *      sub: string,
     * } $permission_enum_class
     * @return null|array{
     *     id: string,
     *     name: string
     * }[]
     */
    public static function list_modules(string|array $permission_enum_class): ?array
    {
        $permissions = [];

        if(is_array($permission_enum_class)) {
            if(@$permission_enum_class['main'] && class_exists($permission_enum_class['main']))
               $permissions = $permission_enum_class['main']::cases();

            if(@$permission_enum_class['sub'] && class_exists($permission_enum_class['sub']))
               $permissions = [...$permissions, ...$permission_enum_class['sub']::cases()];

            if(empty($permissions))
                return null;
        } else {
            if (!class_exists($permission_enum_class))
                return null;

            $permissions = $permission_enum_class::cases();
        }

        $access_types = PermissionAccessType::array_string();

        $arr = [];

        /** @var PermissionInterface $permissions */
        foreach($permissions as $v) {
            $arr[] = [
                "id" => $v->name,
                "name" => $v->value ?? str_replace("_", " ", $v->name),
                "access" => LayArray::map($access_types, fn($a) => ["type" => ucwords(strtolower($a)), "value" => $a . "." . $v->name])
            ];
        }

        return $arr;
    }

    /**
     * An array of permissions as stringed values. Example: `["VIEW.Blog", "EDIT.Blog"]`.
     * This function eliminates unnecessary permissions; so if `ALL.Blog` for example is available,
     * it doesn't make sense having `VIEW.blog` there as well.
     *
     * @param array<string> $permissions
     * @return array<int, array{
     *   type: string,
     *   value: string,
     * }>
     */
    public static function unique_access(array $permissions) : array
    {
        $list = [];

        $current_all_module = "";
//        $current_module = "";
//        $all_found = 0;

        foreach ($permissions as $permission) {
            $entry = explode(".", $permission, 2);
            $access = $entry[0];
            $module = $entry[1] ?? null;

            if($current_all_module == $module)
                continue;

            if($access == "ALL")
                $current_all_module = $module;

//            $current_module = $module;
            $list[] = $permission;
        }

        return $list;
    }

    /**
     * An alias for unique access
     * @see unique_access
     * @param array $permissions
     * @return array<int, array{
     * type: string,
     * value: string,
     * }>
     */
    public static function serialize(array $permissions) : array
    {
        return self::unique_access($permissions);
    }

    /**
     * @param PermissionInterface $module
     * @return array{
     *     module: PermissionInterface,
     *     access: array{
     *        ALL: string,
     *        VIEW: string,
     *        CREATE: string,
     *        EDIT: string,
     *        DELETE: string,
     *     },
     * }
     */
    public static function access_obj(PermissionInterface $module) : array
    {
        $access_types = PermissionAccessType::array_string();
        $access = [];

        foreach ($access_types as $a) {
            $access[$a] = $a . "." . $module->name;
        }

        return [
            "module" => $module,
            "access" => $access
        ];
    }
}