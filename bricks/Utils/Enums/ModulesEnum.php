<?php

declare(strict_types=1);

namespace Elevator\Utils\Enums;

use Elevator\Utils\Interface\PermissionInterface;

enum ModulesEnum : string implements PermissionInterface
{
    case Users = "See and Create Users";
    case UserRole = "User Roles";
    case UserPosition = "User Positions";
    case UserActivityLog = "Systemwide Activity Log";

    case Post = "Can Access Blog Module";
    case PostPublish = "Can Publish Blog Posts";
    case PostCalendar = "Can Modify Blog Calendar";
    case PostCategory = "Can Modify Blog Category/Collection";
    case PostApprovalRevision = "Can See All Blog Revision Notes";

    case AiAgents = "Evai agents";
    case AiAgentWorkflow = "Evai agents workflow";
    case AiAgentServiceWorker = "Evai agents service workers";
    case AiAgentActions = "Evai agents Independent Actions";

    case SystemLog = "Log Activities of System Users";
    case Brand = "Modify Brand details, testimony, faq, teams, etc";
    case FileStore = "Project File Storage";

    case Settings = "General settings";
    case HTML = "HTML Components";

    public function owner(): self
    {
        return $this;
    }

}
