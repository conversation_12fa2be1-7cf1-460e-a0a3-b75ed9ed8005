<?php

namespace Elevator\Utils;

use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use Elevator\Utils\Traits\HasFoundation;

abstract class ElevatorController
{
    use ControllerHelper;
    use HasFoundation;

    public static function new() : static
    {
        return new static();
    }

    abstract public function model() : BaseModelHelper;
}