<?php

namespace Elevator\Utils\Middlewares;

use <PERSON><PERSON><PERSON>er\Lay\Core\Api\Enums\ApiStatus;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use Elevator\User\Controller\ElevatorAuthSession;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Enums\PermissionAccessType;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;

class ElevatorMiddleware
{
    protected static ElevatorAuthSession $session;

    use ControllerHelper;

    public function __construct(
        protected readonly ElevatorBaseHook $base_hook,
        public string                       $prefix = "elv",
    )
    {
        self::$session = $this->base_hook::foundation()->user_session();
    }

    public function set_prefix(string $prefix) : void
    {
        $this->prefix = $prefix;
    }

    public function is_auth() : void
    {
        $this->base_hook->group_middleware(
            fn() => $this->base_hook::foundation()->use_php_session() ? self::$session::validate_session() : self::$session::validate_jwt()
        );
    }

    public function is_sys_arch() : void
    {
        $this->base_hook->group_middleware(
            fn() => self::$session::get('is_super_admin') ?
                self::res_success("Proceed") :
                self::res_warning("Unauthorized Access", code: ApiStatus::UNAUTHORIZED),
        );
    }

    public function is_permitted(
        HasPermission|PermissionInterface $module,
        PermissionAccessType $access = PermissionAccessType::VIEW
    ) : callable
    {
        return fn() =>
            $this->base_hook::foundation()->user_role()::permitted($module, $access) ?
                self::res_success("Proceed") :
                self::res_warning("You don't have access to perform this operation", code: ApiStatus::UNAUTHORIZED)
        ;
    }
}