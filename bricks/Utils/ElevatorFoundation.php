<?php

namespace Elevator\Utils;

use BrickLayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Core\View\Domain;
use BrickLayer\Lay\Libs\Mail\Mailer;
use Elevator\AiAgent\Controller\ElevatorAgentActions;
use Elevator\AiAgent\Controller\ElevatorAgentActivityController;
use Elevator\AiAgent\Controller\ElevatorAgentController;
use Elevator\AiAgent\Controller\ElevatorAgentServiceWorkerController;
use Elevator\AiAgent\Controller\ElevatorAgentWorkflowController;
use Elevator\Blog\Controller\ElevatorPostCalendarController;
use Elevator\Blog\Controller\ElevatorPostCategories;
use Elevator\Blog\Controller\ElevatorPostCollections;
use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Blog\Controller\ElevatorPostReactions;
use Elevator\Blog\Controller\ElevatorPostTags;
use Elevator\Brand\Controller\ElevatorBrandController;
use Elevator\Brand\Controller\ElevatorBrandFaqController;
use Elevator\Brand\Controller\ElevatorBrandNewsletterController;
use Elevator\Brand\Controller\ElevatorBrandPartnerController;
use Elevator\Brand\Controller\ElevatorBrandProspectController;
use Elevator\Brand\Controller\ElevatorBrandTeamController;
use Elevator\Brand\Controller\ElevatorBrandTestimonyController;
use Elevator\Country\Controller\ElevatorCountryController;
use Elevator\Country\Controller\ElevatorStateController;
use Elevator\FileStore\Controller\ElevatorFileStoreController;
use Elevator\FileStore\Controller\ElevatorFileStoreTypeController;
use Elevator\HtmlComponent\Controller\ElevatorHtmlFormController;
use Elevator\SitemapRss\Controller\ElevatorRssChannels;
use Elevator\SitemapRss\Controller\ElevatorSitemapController;
use Elevator\SysArch\Controller\ElevatorSystemArch;
use Elevator\User\Controller\ElevatorActivityLogController;
use Elevator\User\Controller\ElevatorAuthController;
use Elevator\User\Controller\ElevatorAuthLogController;
use Elevator\User\Controller\ElevatorAuthSession;
use Elevator\User\Controller\ElevatorPositionController;
use Elevator\User\Controller\ElevatorProfileController;
use Elevator\User\Controller\ElevatorRefreshTokenController;
use Elevator\User\Controller\ElevatorRoleController;
use Elevator\Utils\Enums\ModulesEnum;

//
// This file contains a list of all the controllers in this project
//

class ElevatorFoundation
{
    ####                ####
    ##      Utils       ##
    ####                ####

    /**
     * Instruct Elevator to process user session with JWT or PHP Session
     * @return  bool
     */
    public function use_php_session() : bool
    {
        return true;
    }

    /**
     * All the email messages the project can send
     * @return ElevatorEmailMessages
     */
    public function mailer() : ElevatorEmailMessages
    {
        return new ElevatorEmailMessages(new Mailer());
    }

    /**
     * This instructs elevator to use the specified classes as your main and sub permissions class.
     *
     * If you want elevator to use both the default permission class and your own, then update `sub` only.
     *
     * If you want to use just your permission class, then update the `main` key and ignore the `sub` key
     *
     * @return array{
     *     main: string,
     *     sub: string,
     * } An array of PermissionInterfaces classes
     */
    public function permission_class() : array
    {
        return [
            "main" => ModulesEnum::class,
            "sub" => null
        ];
    }

    /**
     * The domain for the project
     * @return string
     */
    public function domain() : string
    {
        return "https://www.osaitech.dev/";
    }

    /**
     * The mock server AI Agent workflow-processor should use when executing
     */
    public function agent_mock_domain() : void
    {
        Domain::new()->mock("boardroom-id", "boardroom.osaitech.dev");
    }

    public function site_author() : string
    {
        return LayConfig::site_data()->author;
    }

    /**
     * @return array{
     *     mail: string,
     *     name: string,
     * }
     */
    public function support_team() : array
    {
        return [
            "mail" => LayConfig::site_data()->mail->{0},
            "name" => "Osai Tech Team"
        ];
    }

    /**
     * @abstract Overwrite to your own R2/S3 bucket
     * @return string
     */
    public function bucket_url() : string
    {
        // Example: "https://static.example.com/";
        return LayConfig::site_data()->others->bucket_domain;
    }

    /**
     * Default super-user details
     *
     * @return array{
     *     max: int,
     *     position: string,
     *     role: string,
     * }
     */
    public function super_user() : array
    {
        return [
            "max" => 1,
            "position" => "Founder",
            "role" => "Prime User",
        ];
    }


    ####                ####
    ##      Ai Agent    ##
    ####                ####
    public function ai_action() : ElevatorAgentActions
    {
        return ElevatorAgentActions::new();
    }

    public function ai_activity() : ElevatorAgentActivityController
    {
        return ElevatorAgentActivityController::new();
    }

    public function ai_agent() : ElevatorAgentController
    {
        return ElevatorAgentController::new();
    }

    public function ai_service_worker() : ElevatorAgentServiceWorkerController
    {
        return ElevatorAgentServiceWorkerController::new();
    }

    public function ai_workflow() : ElevatorAgentWorkflowController
    {
        return ElevatorAgentWorkflowController::new();
    }


    ####                ####
    ##      Blog Post   ##
    ####                ####
    public function post_calendar() : ElevatorPostCalendarController
    {
        return ElevatorPostCalendarController::new();
    }

    public final function post_category() : ElevatorPostCategories
    {
        return ElevatorPostCategories::new();
    }

    public final function post_collection() : ElevatorPostCollections
    {
        return ElevatorPostCollections::new();
    }

    public function post_ctrl() : ElevatorPostController
    {
        return ElevatorPostController::new();
    }

    public function post_reaction() : ElevatorPostReactions
    {
        return ElevatorPostReactions::new();
    }

    public function post_tag() : ElevatorPostTags
    {
        return ElevatorPostTags::new();
    }


    ####                ####
    ##      Brand       ##
    ####                ####
    public function brand_ctrl() : ElevatorBrandController
    {
        return ElevatorBrandController::new();
    }

    public function brand_faq() : ElevatorBrandFaqController
    {
        return ElevatorBrandFaqController::new();
    }

    public function brand_testimony() : ElevatorBrandTestimonyController
    {
        return ElevatorBrandTestimonyController::new();
    }

    public function brand_partner() : ElevatorBrandPartnerController
    {
        return ElevatorBrandPartnerController::new();
    }

    public function brand_team() : ElevatorBrandTeamController
    {
        return ElevatorBrandTeamController::new();
    }

    public function brand_prospect() : ElevatorBrandProspectController
    {
        return ElevatorBrandProspectController::new();
    }

    public function brand_newsletter() : ElevatorBrandNewsletterController
    {
        return ElevatorBrandNewsletterController::new();
    }

    ####                ####
    ##      File Store  ##
    ####                ####
    public function file_store() : ElevatorFileStoreController
    {
        return ElevatorFileStoreController::new();
    }

    public function file_store_type() : ElevatorFileStoreTypeController
    {
        return ElevatorFileStoreTypeController::new();
    }

    ####                    ####
    ##      HTML Component   ##
    ####                    ####
    public function html_form() : ElevatorHtmlFormController
    {
        return ElevatorHtmlFormController::new();
    }

    ####                    ####
    ##      Sitemap & Rss   ##
    ####                    ####
    public function sitemap_ctrl() : ElevatorSitemapController
    {
        return ElevatorSitemapController::new();
    }

    public function rss_ctrl() : ElevatorRssChannels
    {
        return ElevatorRssChannels::new();
    }

    ####                ####
    ##      Sys Arch    ##
    ####                ####

    public function sys_arch() : ElevatorSystemArch
    {
        return ElevatorSystemArch::new();
    }

    ####                ####
    ##      Country    ##
    ####                ####

    public function country() : ElevatorCountryController
    {
        return ElevatorCountryController::new();
    }

    public function state() : ElevatorStateController
    {
        return ElevatorStateController::new();
    }


    ####                ####
    ##      Users       ##
    ####                ####
    public function user_auth() : ElevatorAuthController
    {
        return ElevatorAuthController::new();
    }

    public function user_activity() : ElevatorActivityLogController
    {
        return ElevatorActivityLogController::new();
    }

    public function user_auth_log() : ElevatorAuthLogController
    {
        return ElevatorAuthLogController::new();
    }

    public function user_session() : ElevatorAuthSession
    {
        return ElevatorAuthSession::new();
    }

    public function user_position() : ElevatorPositionController
    {
        return ElevatorPositionController::new();
    }

    public function user_profile() : ElevatorProfileController
    {
        return ElevatorProfileController::new();
    }

    public function user_refresh_token() : ElevatorRefreshTokenController
    {
        return ElevatorRefreshTokenController::new();
    }

    public function user_role() : ElevatorRoleController
    {
        return ElevatorRoleController::new();
    }


    ###
    ### Glue that holds Elevator to your project
    ###
    public static final function new() : static
    {
        return new static();
    }
}