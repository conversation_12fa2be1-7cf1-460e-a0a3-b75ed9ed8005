<?php

namespace Elevator\Utils;

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use BrickLayer\Lay\Core\View\Tags\Anchor;
use <PERSON>Layer\Lay\Libs\LayDate;
use BrickLayer\Lay\Libs\Mail\Mailer;
use Elevator\Utils\Traits\HasFoundation;

class ElevatorEmailMessages
{
    use HasFoundation;

    public function __construct(public readonly Mailer $mailer){}

    public function email_btn(string $link, string $text): string
    {
        if(method_exists($this->mailer, "email_btn")) return $this->mailer::email_btn($link, $text);

        $color = LayConfig::site_data()->color->pry;
        return <<<BTN
            <span style="display: block; width: 100%; margin: 10px 0">
                <a style="
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 1;
                    display: inline-block;
                    text-align: center;
                    text-decoration: none;
                    background: $color;
                    color: #fff;
                    padding: 15px;
                    border-radius: 5px;
                " href="$link">$text</a>
            </span>
        BTN;

    }

    public function welcome_admin(array $data): ?bool
    {
        $link = Anchor::new()->href('sign-in')->get_href();
        $btn = self::email_btn($link, 'Checkout The Dashboard');
        $company = LayConfig::site_data()->name->short;

        return $this->mailer->subject("Welcome Onboard " . $data['name'])
            ->client($data['email'], $data['name'])
            ->body(<<<message
                <h2>Hurray!!! You Have An Account On Our Platform</h2>
                <p>
                    This email is a formal welcome to the platform; Today, you are a part of something great; 
                    please familiarize yourself with the dashboard because you will be doing a lot there. 
                </p>
                <p>
                    Kindly login with your email and use the password below.
                    Be sure to update your password as soon as you login.
                </p>
                <div>
                    <blockquote>
                        <b style="font-size: 25px !important;">{$data['password']}</b>
                    </blockquote>
                </div>
                <p>
                    Welcome once again from all of us at $company. <br> 
                    {$btn}
                </p>
                <p>
                    Best Regards
                </p>
            message
            )
            ->to_client();
    }

    public function notify_editor(array $data): ?bool
    {
        $to_email = $data['editors'][0]['email'];
        $to_name = $data['editors'][0]['name'];

        unset($data['editors'][0]);

        $btn = self::email_btn($data['link'], $data['blog']['title']);

        return $this->mailer->subject("[Important] New Article Up For Review!")
            ->client($to_email, $to_name)
            ->cc(...$data['editors'])
            ->body(<<<message
                <p>Hello Editors,</p>
                
                <p>
                    This is a formal notice that <b>{$data['author']['name']}</b> has created an article and is requesting an approval. $btn
                </p>
                 
                <p>Please review the article as soon as possible, because without your approval, it will never see the light of the day.</p>
                
                <p>If there are modifications that need to be made, please effect them. If the writer needs to rewrite the whole article, please use the status section to pass the information.</p>
                
                <p>Warm regards.</p>
            message
            )
            ->to_client();
    }

    public function notify_author(array $data): ?bool
    {
        $btn = self::email_btn($data['link'], $data['blog']['title']);

        return $this->mailer->subject("[Important] Your Article Needs Improvement!")
            ->client($data['email'], $data['name'])
            ->body(<<<MSG
                <p>Hello {$data['name']},</p>
                
                <p>
                    Your article has been reviewed, but there's more work to be done. Please log into the dashboard to modify your post.
                </p>
                <div>
                    $btn            
                </div>
                
                <p>You will find the review-note on the blog-status section. It is also attached here for your perusal and further actions.</p>
                
                <p style="background: #f2f1fe; font-style: italic">{$data['note']}</p>
                
                <p>Warm regards.</p>
            MSG)
            ->to_client();
    }

    public function reset_password(array $data) : ?bool
    {
        $link = Anchor::new()->href("new-password/" . $data['token'])->get_href();
        $btn = self::email_btn($link, 'Continue');
        $expires = LayDate::date($data['expires'], 3);

        return $this->mailer
            ->subject("Password Reset Request")
            ->client($data['email'], $data['name'])
            ->preview_text("You request to reset your password has been sent")
            ->body(<<<BDY
            <p>Hello {$data['name']},</p>
            <p>You have placed a request to reset your password, please use the button below to complete the process.</p>
            <div>$btn</div>
            <p><b>Note:</b> This link expires at <b>$expires</b>.</p>
            <p>Thanks and Regards</p>   
            BDY)
            ->to_client(false);
    }

    public function notify_author_calendar_assignment(array $data): ?bool
    {
        $link = Anchor::new()->href("blog/calendar?entry=" . $data['calendar_id'], 'office-id')->get_href();

        $btn = self::email_btn($link, "View Content");

        return $this->mailer->subject("[Important] You have been assigned a post")
            ->preview_text("As an esteemed writer, a post has been assigned to you")
            ->client($data['email'], $data['name'])
            ->body(<<<MSG
                <p>Hello {$data['name']},</p>
                
                <p>
                    A new post has been assigned to you via the platform's dashboard, check it out and ensure to create the post on or before the publish date.
                </p>
                <div>
                    $btn            
                </div>
                
                <p>Warm regards.</p>
            MSG)
            ->to_client();
    }

    public function welcome_newsletter(array $data): ?bool
    {
        $site = self::foundation()->support_team();

        $company = $site['name'];
        $contact = $site['mail'];

        return $this->mailer
            ->subject("Welcome to $company Newsletter!")
            ->client($data['email'], $data['name'])
            ->body(<<<MSG
                <p>Dear {$data['name']},</p>
                
                <p>
                    Welcome to $company! We're thrilled to have you on board our newsletter. 
                    Thank you for subscribing and joining our community of <i>problem solvers that make money as a side effect</i>.
                </p>
                
                <p>
                    At $company, we're passionate about providing solutions, knowing our clients on a personal level, 
                    intimating with our goals, and we hope to bring this mentality to you. In a nutshell, we are solution-driven, 
                    and we want to bring in as many people as we can into this way of thinking and doing things.
                </p> 
                
                <p>Through our newsletter, you'll receive:</p>
                
                <ul>
                    <li>Exclusive updates on our latest products/services/events</li>
                    <li>Insightful industry news and trends</li>
                    <li>Special offers, promotions and freebies</li>
                    <li>Update of the latest job openings in our company</li>
                </ul>
                
                <p>We value your trust and promise to deliver valuable content straight to your inbox. We're excited to share our journey with you and to have you as part of our growing community!</p>
                
                <p>Feel free to reach out to us anytime at <a href="mailto:$contact">$contact</a> if you have any questions, feedback, or just want to say hello.</p>
                
                <p>Thank you once again for joining us. Let's embark on this exciting journey together!</p>
                
                <p>Warm regards.</p>
                
                <p>$company</p>
            MSG)
            ->to_client();
    }
}