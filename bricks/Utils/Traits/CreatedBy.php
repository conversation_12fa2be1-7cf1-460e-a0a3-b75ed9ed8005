<?php

namespace Elevator\Utils\Traits;

/**
 * @abstract  You can overwrite to use your own
 */
trait CreatedBy
{
    public function created_by(): ?string
    {
        return self::foundation()->user_session()::current_user();
    }

    public function client_id(bool $allow_null = false): ?string
    {
        $client_id = self::foundation()->user_session()::current_client();

        if(!$client_id && $allow_null)
            return null;

        return $client_id ?? "default-elevator-client";
    }

}