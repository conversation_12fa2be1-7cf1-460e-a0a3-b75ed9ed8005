<?php

namespace Elevator\Utils\Traits;

use Elevator\User\Model\ElevatorActivityLogModel;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Interface\PermissionInterface;

trait ActivityLog
{
    public function activity_log(
        LogActivityTypes $activity,
        string $message, ?PermissionInterface $module_id = null,
        ?string $created_by = null,
        ?string $client_id = null
    ): void
    {
        (new ElevatorActivityLogModel())->log(
            $message,
            $created_by ?? static::foundation()->user_session()::current_user(),
            $client_id,
            [
                "activity_enum" => $activity,
                "module_enum" => $module_id ?? static::module_id(),
            ]
        );
    }
}