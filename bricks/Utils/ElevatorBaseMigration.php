<?php

namespace Elevator\Utils;

use <PERSON><PERSON><PERSON><PERSON>\Lay\Orm\Enums\OrmDriver;
use <PERSON><PERSON><PERSON><PERSON>\Lay\Orm\SQL;
use Phinx\Db\Table;
use Phinx\Migration\AbstractMigration;

abstract class ElevatorBaseMigration extends AbstractMigration
{
    protected Table $migration;

    abstract protected function model_table() : string;

    abstract protected function model_columns() : array;

    /**
     * @return array<string>
     */
    protected function exclude_columns() : array
    {
        return [];
    }

    /**
     * Use this to do any additional thing you want on the migration.
     * You can even add new columns to the model_columns here;
     *
     * @param Table $migration
     * @return void
     */
    protected function pre_create(Table $migration) : void {}

    protected function default_cols() : array
    {
        return [
            "client_id" => [
                "type" => "char",
                "default" => "default-elevator-client",
                "null" => false,
                "length" => 36,
            ],
            "deleted" => [
                "type" => "integer",
                "length" => 1,
                "default" => 0,
                "null" => false
            ],
            "deleted_by" => [
                "type" => "char",
                "length" => 36,
                "null" => true
            ],
            "deleted_at" => [
                "type" => "biginteger",
                "null" => true
            ],
            "created_by" => [
                "type" => "char",
                "length" => 36,
                "null" => true
            ],
            "created_at" => [
                "type" => "biginteger",
                "null" => false
            ],
            "updated_by" => [
                "type" => "char",
                "length" => 36,
                "null" => true
            ],
            "updated_at" => [
                "type" => "biginteger",
                "null" => true
            ],

            // Add a json column for future. In case there needs to be a new column and we can't add it for whatever reason
            "posterity" => [
                "type" => "jsonb",
                "null" => true
            ],
        ];
    }

    public function migrate_model() : void
    {
        $this->migration = $this->table($this->model_table(), [ "id" => false, "primary_key" => "id"]);
        $this->migration->addColumn("id", "char", [ "length" => 36, "null" => false ]);

        $driver = SQL::get_driver();
        $exclude = $this->exclude_columns();

        $schema_props = array_merge($this->default_cols(), $this->model_columns());

        foreach($schema_props as $col => $props) {
            if(in_array($col, $exclude))
                continue;

            $type = $props['type'];

            if($driver == OrmDriver::MYSQL) {
                $type = $type == "jsonb" ? "json" : $type;
            }

            $unique = $props['unique'] ?? false;

            unset($props['type'], $props['unique']);

            $this->migration->addColumn($col, $type, $props);

            // Simple adding of index and making it unique
            if($unique)
                $this->migration->addIndex([ $col ], [ 'unique' => true ]);
        }

        // Use this function to do more complicated things
        $this->pre_create($this->migration);

        $this->migration->create();
    }
}