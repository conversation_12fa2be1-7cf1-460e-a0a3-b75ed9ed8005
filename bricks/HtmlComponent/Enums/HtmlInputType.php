<?php

namespace Elevator\HtmlComponent\Enums;

use BrickLayer\Lay\Libs\Primitives\Enums\EnumHelper;

enum HtmlInputType
{
    case BUTTON;
    case CHECKBOX;
    case COLOR;
    case DATE;
    case DATETIME_LOCAL;
    case EMAIL;
    case FILE;
    case HIDDEN;
    case MONTH;
    case NUMBER;
    case PASSWORD;
    case RADIO;
    case RANGE;
    case RESET;
    case SEARCH;
    case SUBMIT;
    case TEL;
    case TEXT;
    case TIME;
    case URL;
    case WEEK;

    use EnumHelper;
}
