<?php
declare(strict_types=1);

namespace Elevator\HtmlComponent\Controller;

use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Brand\Model\ElevatorBrandModel;
use Elevator\Brand\Requests\UpdateBrandRequest;
use Elevator\Brand\Resources\BrandResource;
use Elevator\HtmlComponent\Model\ElevatorHtmlFormModel;
use Elevator\Utils\ElevatorController;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Enums\ModulesEnum;
use Elevator\Utils\Interface\HasLog;
use Elevator\Utils\Interface\HasPermission;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;

class ElevatorHtmlFormController extends ElevatorController implements HasPermission, HasLog
{
    /**
     * @abstract  You can overwrite to use your own
     */
    use ActivityLog;

    /**
     * @abstract  You can overwrite to use your own
     */
    public static function module_id(): PermissionInterface
    {
        return ModulesEnum::HTML;
    }

    /**
     * @abstract  You can overwrite to use your own
     */
    public function model(): ElevatorHtmlFormModel
    {
        return new ElevatorHtmlFormModel();
    }



}
