<?php
declare(strict_types=1);

namespace Elevator\HtmlComponent\Model;

use BrickLayer\Lay\Libs\Primitives\Abstracts\BaseModelHelper;
use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Elevator\HtmlComponent\Enums\HtmlElementType;
use Elevator\HtmlComponent\Enums\HtmlInputType;
use Elevator\Utils\Traits\CreatedBy;
use Elevator\Utils\Traits\HasFoundation;

/**
 * @property string name
 * @property HtmlElementType element
 * @property HtmlInputType type
 * @property bool has_options
 * @property string|null placeholder
 * @property string|null default_value
 * @property array attributes
 *
 * @property string id
 * @property string client_id
 * @property bool deleted
 * @property string deleted_by
 * @property int deleted_at
 * @property string|null created_by
 * @property int created_at
 * @property string|null updated_by
 * @property int updated_at
 * @property array posterity
 */
class ElevatorHtmlFormModel extends BaseModelHelper
{
    public static string $table = "elevator_html_component_forms";

    use CreatedBy;
    use HasFoundation;

    protected function cast_schema() : void
    {
        $this->cast("element", HtmlElementType::class, default_value: null, parser: fn($v) => HtmlElementType::to_enum($v));
        $this->cast("type", HtmlInputType::class, default_value: null, parser: fn($v) => HtmlInputType::to_enum($v));
        $this->cast("has_options", "bool", false);
        $this->cast("attributes", "array", []);
    }

    public function is_duplicate(array|RequestHelper $columns) : bool
    {
        $columns = $this->req_2_array($columns);

        return $this->count("name", $columns['name']) > 0;
    }
}
