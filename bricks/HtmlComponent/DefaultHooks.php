<?php

namespace Elevator\HtmlComponent;

use Elevator\HtmlComponent\Controller\ElevatorHtmlFormController;
use Elevator\Utils\ElevatorBaseHook;
use Elevator\Utils\Traits\HasFoundation;

class DefaultHooks extends ElevatorBaseHook
{
    protected static ElevatorHtmlFormController $form;

    protected function primary_prefix() : void
    {
        $this->prefix($this->elevator_middleware()->prefix . "/ht-comp");
    }

    protected function pre_hook() : void
    {
        self::$form = self::foundation()->html_form();
    }

    protected function hooks(): void
    {
        $this->primary_prefix();
        $this->elevator_middleware()->is_auth();

    }
}